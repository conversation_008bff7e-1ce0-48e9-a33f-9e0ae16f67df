package com.ruoyi.base.service;

import java.util.List;
import com.ruoyi.base.domain.BaseVenueInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.base.vo.SiteOccupationVO;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 场地信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBaseVenueInfoService extends IService<BaseVenueInfo> {
    /**
     * 查询场地信息列表
     *
     * @param baseVenueInfo 场地信息
     * @return 场地信息集合
     */
    public List<BaseVenueInfo> selectList(BaseVenueInfo baseVenueInfo);

    List<SiteOccupationVO> getSiteOccupationList(String venueType);

}
