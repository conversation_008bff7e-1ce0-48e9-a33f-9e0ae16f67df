package com.ruoyi.busi.controller;

import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.service.IBusiWinningBidderNoticeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 中标结果公告信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "中标结果公告信息管理")
@RestController
@RequestMapping("/bidder/notice")
public class BusiWinningBidderNoticeController extends BaseController {
    @Autowired
    private IBusiWinningBidderNoticeService busiWinningBidderNoticeService;

    /**
     * 查询中标结果公告信息列表
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:list')")
    @ApiOperation(value = "查询中标结果公告信息列表")
    @GetMapping("/list")
    public TableDataInfo list(BusiWinningBidderNotice busiWinningBidderNotice) {
        startPage();
        List<BusiWinningBidderNotice> list = busiWinningBidderNoticeService.selectList(busiWinningBidderNotice);
        return getDataTable(list);
    }

    /**
     * 导出中标结果公告信息列表
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:export')")
    @Log(title = "中标结果公告信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出中标结果公告信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiWinningBidderNotice busiWinningBidderNotice) {
        List<BusiWinningBidderNotice> list = busiWinningBidderNoticeService.selectList(busiWinningBidderNotice);
        ExcelUtil<BusiWinningBidderNotice> util = new ExcelUtil<BusiWinningBidderNotice>(BusiWinningBidderNotice.class);
        util.exportExcel(response, list, "中标结果公告信息数据");
    }

    /**
     * 获取中标结果公告信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:query')")
    @ApiOperation(value = "获取中标结果公告信息详细信息")
    @ApiImplicitParam(name = "noticeId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId) {
        return success(busiWinningBidderNoticeService.getById(noticeId));
    }

    @ApiOperation(value = "查询采购公告详细信息")
    @GetMapping("/view")
    public AjaxResult view(Long noticeId){
        try{
        BusiTenderVo vo = busiWinningBidderNoticeService.view(noticeId);
        return AjaxResult.success(vo);
        }catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    /**
     * 新增中标结果公告信息
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:add')")
    @Log(title = "中标结果公告信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增中标结果公告信息")
    @PostMapping
    public AjaxResult add(@RequestBody BusiWinningBidderNotice busiWinningBidderNotice) {
        busiWinningBidderNotice.setCreateBy(getUsername());
        busiWinningBidderNotice.setUpdateBy(getUsername());
        return busiWinningBidderNoticeService.saveBusiWinningBidderNotice(busiWinningBidderNotice);
    }


    /**
     * TODO 看是否需要创建表
     * 发布成交通知书
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:add')")
    @Log(title = "发布成交通知书", businessType = BusinessType.INSERT)
    @ApiOperation(value = "发布成交通知书")
    @PostMapping("/pushNotice")
    public AjaxResult pushNotice(@RequestBody BusiWinningBidderNotice busiWinningBidderNotice) {

        return busiWinningBidderNoticeService.pushNotice(busiWinningBidderNotice);
    }

    /**
     * TODO 看是否需要创建表
     * 发布成交通知书
     */
    @GetMapping("/createNoticeContent")
    public AjaxResult createNoticeContent(Long projectId, Long bidderInfoId) {
        try {
//            String res = busiWinningBidderNoticeService.createNoticeContent(projectId, bidderInfoId);
            String res = "";
            return AjaxResult.success("", res);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.error();
    }

    /**
     * 发布流标公告
     */
    @PostMapping("/abortiveTenderNotice")
    public AjaxResult abortiveTenderNotice(@RequestBody BusiWinningBidderNotice busiWinningBidderNotice) {
        try {
            return busiWinningBidderNoticeService.saveAbortiveTenderNotice(busiWinningBidderNotice);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.error();
    }


    /**
     * 修改中标结果公告信息
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:edit')")
    @Log(title = "中标结果公告信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改中标结果公告信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiWinningBidderNotice busiWinningBidderNotice) {
        busiWinningBidderNotice.setUpdateBy(getUsername());
        return toAjax(busiWinningBidderNoticeService.updateById(busiWinningBidderNotice));
    }

    /**
     * 删除中标结果公告信息
     */
    @PreAuthorize("@ss.hasPermi('bidder:notice:remove')")
    @Log(title = "中标结果公告信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除中标结果公告信息")
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        return toAjax(busiWinningBidderNoticeService.removeByIds(Arrays.asList(noticeIds)));
    }
}
