package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 专家专业分类对象 base_expert_profession_classification
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public class BaseExpertProfessionClassification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分类编码 */
    @Excel(name = "分类编码")
    private String classificationCode;

    /** 分类等级 */
    @Excel(name = "分类等级")
    private Integer classificationLevel;

    /** 分类名称 */
    @Excel(name = "分类名称")
    private String classificationName;

    /** 分类排序 */
    @Excel(name = "分类排序")
    private Long classificationSort;

    /** 有效标识0无效1有效
 */
    @Excel(name = "有效标识0无效1有效 ")
    private Integer validFlag;

    /** 删除标识0正常1删除 */
    @TableLogic(value = "0" , delval = "1")
    private Integer delFlag;

    public void setClassificationCode(String classificationCode)
    {
        this.classificationCode = classificationCode;
    }

    public String getClassificationCode() 
    {
        return classificationCode;
    }
    public void setClassificationLevel(Integer classificationLevel)
    {
        this.classificationLevel = classificationLevel;
    }

    public Integer getClassificationLevel()
    {
        return classificationLevel;
    }
    public void setClassificationName(String classificationName) 
    {
        this.classificationName = classificationName;
    }

    public String getClassificationName() 
    {
        return classificationName;
    }
    public void setClassificationSort(Long classificationSort) 
    {
        this.classificationSort = classificationSort;
    }

    public Long getClassificationSort() 
    {
        return classificationSort;
    }
    public void setValidFlag(Integer validFlag) 
    {
        this.validFlag = validFlag;
    }

    public Integer getValidFlag() 
    {
        return validFlag;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("classificationCode", getClassificationCode())
            .append("classificationLevel", getClassificationLevel())
            .append("classificationName", getClassificationName())
            .append("classificationSort", getClassificationSort())
            .append("validFlag", getValidFlag())
            .append("delFlag", getDelFlag())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
