package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BusiAuditProcessEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业信息对象 base_ent_info
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("企业信息对象")
@TableName(resultMap = "com.ruoyi.base.mapper.BaseEntInfoMapper.BaseEntInfoResult")
public class BaseEntInfo extends BusiAuditProcessEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long entId;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    @Excel(name = "企业名称")
    private String entName;

    /**
     * 企业名称
     */
    @ApiModelProperty("密钥")
    @Excel(name = "密钥")
    private String secretKey;
    /**
     * 统一社会信用代码
     */
    @ApiModelProperty("统一社会信用代码")
    @Excel(name = "统一社会信用代码")
    private String entCode;
    /**
     * 经济性质
     */
    @ApiModelProperty("经济性质")
    @Excel(name = "经济性质")
    private String entNature;

    /**
     * 经济性质
     */
    @ApiModelProperty("法人身份证明")
    private String entLegalPersonCardFile;
    /**
     * 企业地址
     */
    @ApiModelProperty("企业地址")
    @Excel(name = "企业地址")
    private String entAddress;
    /**
     * 重点代理领域
     */
    @ApiModelProperty("重点代理领域")
    @Excel(name = "重点代理领域")
    private String entKeyAgencyAreas;
    /**
     * 开户行
     */
    @ApiModelProperty("开户行")
    @Excel(name = "开户行")
    private String entOpeningBank;
    /**
     * 开户行户号
     */
    @ApiModelProperty("开户行户号")
    @Excel(name = "开户行户号")
    private String entBankCode;
    /**
     * 单位网址
     */
    @ApiModelProperty("单位网址")
    @Excel(name = "单位网址")
    private String entWebsite;
    /**
     * 企业简介
     */
    @ApiModelProperty("企业简介")
    @Excel(name = "企业简介")
    private String entIntro;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @Excel(name = "联系人")
    private String entLinkman;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @Excel(name = "联系方式")
    private String entContactPhone;
    /**
     * 法人
     */
    @ApiModelProperty("法人")
    @Excel(name = "法人")
    private String entLegalPerson;
    /**
     * 法人联系方式
     */
    @ApiModelProperty("法人联系方式")
    @Excel(name = "法人联系方式")
    private String entLegalPersonPhone;
    @ApiModelProperty("邮编")
    private String zipCode;


    /**
     * 企业状态
     */
    @ApiModelProperty("企业状态")
    @Excel(name = "企业状态")
    private Integer entStatus;
    /**
     * 企业LOGO
     */
    @ApiModelProperty("企业LOGO")
    @Excel(name = "企业LOGO")
    private String entLogo;
    /**
     * 企业类型
     */
    @ApiModelProperty("企业类型")
    @Excel(name = "企业类型")
    private Integer entType;

    @ApiModelProperty("营业执照")
    @Excel(name = "营业执照")
    private String businessLicense;

    /**
     * 开始时间
     */
    @ApiModelProperty("成立时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date establishmentTime;


    @ApiModelProperty("经营期限")
    private Integer operatingPeriod;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private String oldSecretKey;
    /**
     * 公司章
     */
    private byte[] enterpriseSignature;
    /**
     * 法定代表人章或签字
     */
    private byte[] legalRepreSignature;

    public byte[] getLegalRepreSignature() {
        return legalRepreSignature;
    }

    public void setLegalRepreSignature(byte[] legalRepreSignature) {
        this.legalRepreSignature = legalRepreSignature;
    }

    public String getOldSecretKey() {
        return oldSecretKey;
    }

    public void setOldSecretKey(String oldSecretKey) {
        this.oldSecretKey = oldSecretKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public Long getEntId() {
        return entId;
    }

    public void setEntId(Long entId) {
        this.entId = entId;
    }

    public String getEntName() {
        return entName;
    }

    public void setEntName(String entName) {
        this.entName = entName;
    }

    public String getEntCode() {
        return entCode;
    }

    public void setEntCode(String entCode) {
        this.entCode = entCode;
    }

    public String getEntNature() {
        return entNature;
    }

    public void setEntNature(String entNature) {
        this.entNature = entNature;
    }

    public String getEntAddress() {
        return entAddress;
    }

    public void setEntAddress(String entAddress) {
        this.entAddress = entAddress;
    }

    public String getEntKeyAgencyAreas() {
        return entKeyAgencyAreas;
    }

    public void setEntKeyAgencyAreas(String entKeyAgencyAreas) {
        this.entKeyAgencyAreas = entKeyAgencyAreas;
    }

    public String getEntOpeningBank() {
        return entOpeningBank;
    }

    public void setEntOpeningBank(String entOpeningBank) {
        this.entOpeningBank = entOpeningBank;
    }

    public String getEntBankCode() {
        return entBankCode;
    }

    public void setEntBankCode(String entBankCode) {
        this.entBankCode = entBankCode;
    }

    public String getEntWebsite() {
        return entWebsite;
    }

    public void setEntWebsite(String entWebsite) {
        this.entWebsite = entWebsite;
    }

    public String getEntIntro() {
        return entIntro;
    }

    public void setEntIntro(String entIntro) {
        this.entIntro = entIntro;
    }

    public String getEntLinkman() {
        return entLinkman;
    }

    public void setEntLinkman(String entLinkman) {
        this.entLinkman = entLinkman;
    }

    public String getEntContactPhone() {
        return entContactPhone;
    }

    public void setEntContactPhone(String entContactPhone) {
        this.entContactPhone = entContactPhone;
    }

    public String getEntLegalPerson() {
        return entLegalPerson;
    }

    public void setEntLegalPerson(String entLegalPerson) {
        this.entLegalPerson = entLegalPerson;
    }

    public String getEntLegalPersonPhone() {
        return entLegalPersonPhone;
    }

    public void setEntLegalPersonPhone(String entLegalPersonPhone) {
        this.entLegalPersonPhone = entLegalPersonPhone;
    }

    public Integer getEntStatus() {
        return entStatus;
    }

    public void setEntStatus(Integer entStatus) {
        this.entStatus = entStatus;
    }

    public String getEntLogo() {
        return entLogo;
    }

    public void setEntLogo(String entLogo) {
        this.entLogo = entLogo;
    }

    public Integer getEntType() {
        return entType;
    }

    public void setEntType(Integer entType) {
        this.entType = entType;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}
