package com.ruoyi.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;

import java.util.List;

/**
 * 企业信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBaseEntInfoService extends IService<BaseEntInfo> {
    /**
     * 查询企业信息列表
     *
     * @param baseEntInfo 企业信息
     * @return 企业信息集合
     */
    public List<BaseEntInfo> selectList(BaseEntInfo baseEntInfo);

    List<BaseEntInfo> getByIds(List<Long> entIds);

    AjaxResult updateAndAudit(BaseEntInfo baseEntInfo);

    AjaxResult resetSecretKey(Long entId);
}