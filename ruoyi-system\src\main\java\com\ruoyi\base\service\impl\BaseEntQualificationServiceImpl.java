package com.ruoyi.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
        import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.base.mapper.BaseEntQualificationMapper;
import com.ruoyi.base.domain.BaseEntQualification;
import com.ruoyi.base.service.IBaseEntQualificationService;

/**
 * 企业资质Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BaseEntQualificationServiceImpl extends ServiceImpl<BaseEntQualificationMapper, BaseEntQualification> implements IBaseEntQualificationService {
    /**
     * 查询企业资质列表
     *
     * @param baseEntQualification 企业资质
     * @return 企业资质
     */
    @Override
    public List<BaseEntQualification> selectList(BaseEntQualification baseEntQualification) {
        QueryWrapper<BaseEntQualification> baseEntQualificationQueryWrapper = new QueryWrapper<>();
                        baseEntQualificationQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntQualification.getEntId()),"ent_id",baseEntQualification.getEntId());
                        baseEntQualificationQueryWrapper.like(ObjectUtil.isNotEmpty(baseEntQualification.getQualificationType()),"qualification_type",baseEntQualification.getQualificationType());
                        baseEntQualificationQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntQualification.getQualificationFile()),"qualification_file",baseEntQualification.getQualificationFile());
                    String beginQualificationStartDate = baseEntQualification.getParams().get("beginQualificationStartDate")!=null?baseEntQualification.getParams().get("beginQualificationStartDate")+"":"";
                    String endQualificationStartDate = baseEntQualification.getParams().get("endQualificationStartDate")+""!=null?baseEntQualification.getParams().get("endQualificationStartDate")+"":"";
                        baseEntQualificationQueryWrapper.between(ObjectUtil.isNotEmpty(beginQualificationStartDate) && ObjectUtil.isNotEmpty(endQualificationStartDate), "qualification_start_date", beginQualificationStartDate , endQualificationStartDate);
                    String beginQualificationEndDate = baseEntQualification.getParams().get("beginQualificationEndDate")!=null?baseEntQualification.getParams().get("beginQualificationEndDate")+"":"";
                    String endQualificationEndDate = baseEntQualification.getParams().get("endQualificationEndDate")+""!=null?baseEntQualification.getParams().get("endQualificationEndDate")+"":"";
                        baseEntQualificationQueryWrapper.between(ObjectUtil.isNotEmpty(beginQualificationEndDate) && ObjectUtil.isNotEmpty(endQualificationEndDate), "qualification_end_date", beginQualificationEndDate , endQualificationEndDate);
                        baseEntQualificationQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntQualification.getIsLongTerm()),"is_long_term",baseEntQualification.getIsLongTerm());
        return list(baseEntQualificationQueryWrapper);
    }
}
