package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiProcessInfo;
import com.ruoyi.busi.service.IBusiProcessInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 全流程信息归档信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Api(tags = "全流程信息归档信息管理")
@RestController
@RequestMapping("/process/info")
public class BusiProcessInfoController extends BaseController {
    @Autowired
    private IBusiProcessInfoService busiProcessInfoService;
    @Autowired
    private IBusiTenderProjectService tenderProjectService;

/**
 * 查询全流程信息归档信息列表
 */
@PreAuthorize("@ss.hasPermi('process:info:list')")
@ApiOperation(value = "查询全流程信息归档信息列表")
@GetMapping("/list")
    public TableDataInfo list(BusiProcessInfo busiProcessInfo) {
        startPage();
        List<BusiProcessInfo> list = busiProcessInfoService.selectList(busiProcessInfo);
        return getDataTable(list);
    }

    /**
     * 导出全流程信息归档信息列表
     */
    @PreAuthorize("@ss.hasPermi('process:info:export')")
    @Log(title = "全流程信息归档信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出全流程信息归档信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiProcessInfo busiProcessInfo) {
        List<BusiProcessInfo> list = busiProcessInfoService.selectList(busiProcessInfo);
        ExcelUtil<BusiProcessInfo> util = new ExcelUtil<BusiProcessInfo>(BusiProcessInfo. class);
        util.exportExcel(response, list, "全流程信息归档信息数据");
    }

    /**
     * 获取全流程信息归档信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('process:info:query')")
    @ApiOperation(value = "获取全流程信息归档信息详细信息")
    @GetMapping(value = "/getByProject")
    public AjaxResult getByProject(Long projectId) {
        QueryWrapper<BusiProcessInfo> busiProcessInfoQueryWrapper = new QueryWrapper<>();
        busiProcessInfoQueryWrapper.eq("project_id", projectId);
        return success(busiProcessInfoService.getOne(busiProcessInfoQueryWrapper));
    }

    /**
     * 获取全流程信息归档信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('process:info:query')")
    @ApiOperation(value = "获取全流程信息归档信息详细信息")
    @ApiImplicitParam(name = "processId", value = "归档id", required = true, dataType = "Long")
    @GetMapping(value = "/{processId}")
    public AjaxResult getInfo(@PathVariable("processId")Long processId) {
        return success(busiProcessInfoService.getById(processId));
    }

    /**
     * 新增全流程信息归档信息
     */
    @PreAuthorize("@ss.hasPermi('process:info:add')")
    @Log(title = "全流程信息归档信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增全流程信息归档信息")
    @PostMapping
    public AjaxResult add(@RequestBody BusiProcessInfo busiProcessInfo) {
        return toAjax(busiProcessInfoService.save(busiProcessInfo));
    }

    /**
     * 修改全流程信息归档信息
     */
    @PreAuthorize("@ss.hasPermi('process:info:edit')")
    @Log(title = "全流程信息归档信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改全流程信息归档信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiProcessInfo busiProcessInfo) {
        busiProcessInfo.setUpdateBy(getUsername());
        boolean b = busiProcessInfoService.updateById(busiProcessInfo);
        if(b){
            BusiTenderProject project = tenderProjectService.getById(busiProcessInfo.getProjectId());
            project.setProjectStatus(200);
            tenderProjectService.updateById(project);
        }
        return toAjax(b);
    }

    /**
     * 删除全流程信息归档信息
     */
    @PreAuthorize("@ss.hasPermi('process:info:remove')")
    @Log(title = "全流程信息归档信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除全流程信息归档信息")
    @DeleteMapping("/{processIds}")
    public AjaxResult remove(@PathVariable Long[] processIds) {
        return toAjax(busiProcessInfoService.removeByIds(Arrays.asList(processIds)));
    }

    /**
     * 获取全流程信息归档信息详细信息
     */
    @ApiOperation(value = "获取全流程信息归档信息附件信息")
    @ApiImplicitParam(name = "processId", value = "归档id", required = true, dataType = "Long")
    @GetMapping(value = "/selectProcessAttachment/{processId}")
    public AjaxResult selectProcessAttachment(@PathVariable("processId")Long processId) {
            return success(busiProcessInfoService.selectProcessAttachment(processId));
    }

    /**
     * 获取全流程信息归档信息详细信息
     */
    @ApiOperation(value = "初始化归档文件")
    @ApiImplicitParam(name = "processId", value = "归档id", required = true, dataType = "Long")
    @PostMapping(value = "/initAttachmentForProcess")
    public AjaxResult initAttachmentForProcess(Long processId) {
        return success(busiProcessInfoService.initAttachmentForProcess(processId));
    }
}
