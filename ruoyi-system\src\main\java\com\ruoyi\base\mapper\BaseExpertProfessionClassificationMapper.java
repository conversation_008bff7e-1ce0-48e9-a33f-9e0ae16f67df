package com.ruoyi.base.mapper;

import java.util.List;
import com.ruoyi.base.domain.BaseExpertProfessionClassification;

/**
 * 专家专业分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface BaseExpertProfessionClassificationMapper 
{
    /**
     * 查询专家专业分类
     * 
     * @param classificationCode 专家专业分类主键
     * @return 专家专业分类
     */
    public BaseExpertProfessionClassification selectBaseExpertProfessionClassificationByClassificationCode(String classificationCode);

    /**
     * 查询专家专业分类列表
     * 
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 专家专业分类集合
     */
    public List<BaseExpertProfessionClassification> selectBaseExpertProfessionClassificationList(BaseExpertProfessionClassification baseExpertProfessionClassification);

    /**
     * 新增专家专业分类
     * 
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 结果
     */
    public int insertBaseExpertProfessionClassification(BaseExpertProfessionClassification baseExpertProfessionClassification);

    /**
     * 修改专家专业分类
     * 
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 结果
     */
    public int updateBaseExpertProfessionClassification(BaseExpertProfessionClassification baseExpertProfessionClassification);

    /**
     * 删除专家专业分类
     * 
     * @param classificationCode 专家专业分类主键
     * @return 结果
     */
    public int deleteBaseExpertProfessionClassificationByClassificationCode(String classificationCode);

    /**
     * 批量删除专家专业分类
     * 
     * @param classificationCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseExpertProfessionClassificationByClassificationCodes(String[] classificationCodes);
}
