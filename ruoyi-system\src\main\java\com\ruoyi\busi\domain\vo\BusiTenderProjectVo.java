package com.ruoyi.busi.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.busi.domain.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BusiTenderProjectVo extends BusiTenderProject {

    /**
     * 采购方式
     */
    private String tenderModeName;
    /**
     * 项目行业
     */
    private String projectIndustryName;
    /**
     * 资金来源
     */
    private String tenderFundSourceName;
    /**
     * 项目所属区域
     */
    private String projectAreaName;

    /**
     * 代理信息
     */
    private BaseEntInfo agency;
    /**
     * 采购人信息
     */
    private BaseEntInfo purchaser;

}
