package com.ruoyi.busi.enums;

import java.util.Arrays;

public enum AbortiveTypeEnum {
    BINDDING(1, "报名流标", "上传响应文件的供应商数量不足3家"),
    SIGNIN(2, "签到流标", "开标签到供应商数量不足3家"),
    EVALUATION(3, "评审流标", "有效参与供应商数量不足3家"),
    DECODE(4, "解密流标", "有效解密供应商数量不足3家"),;

    private Integer code;
    private String name;
    private String remark;

    AbortiveTypeEnum(Integer code, String name, String remark) {
        this.code = code;
        this.name = name;
        this.remark = remark;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getRemark() {
        return remark;
    }

    public static AbortiveTypeEnum getRemarkByCode(Integer code){
        return Arrays.stream(AbortiveTypeEnum.values()).filter(rel -> rel.code.equals(code)).findFirst().get();
    }
}
