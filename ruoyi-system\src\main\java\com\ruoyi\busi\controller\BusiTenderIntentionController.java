package com.ruoyi.busi.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiTenderIntention;
import com.ruoyi.busi.service.IBusiTenderIntentionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 采购意向Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "采购意向管理")
@RestController
@RequestMapping("/tender/intention")
public class BusiTenderIntentionController extends BaseController {
    @Autowired
    private IBusiTenderIntentionService busiTenderIntentionService;

    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;

    /**
     * 查询采购意向列表
     */
//    @PreAuthorize("@ss.hasPermi('tender:intention:list')")
    @ApiOperation(value = "查询采购意向列表")
    @GetMapping("/list")
    public TableDataInfo list(BusiTenderIntention busiTenderIntention) {

        if(busiTenderIntention.getParams().containsKey("isScope")){
            busiTenderIntention.getParams().put("isScope","isScope");
        }
        startPage();
        List<BusiTenderIntention> list = busiTenderIntentionService.selectList(busiTenderIntention);
        return getDataTable(list);
    }

    /**
     * 导出采购意向列表
     */
    @PreAuthorize("@ss.hasPermi('tender:intention:export')")
    @Log(title = "采购意向", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购意向列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiTenderIntention busiTenderIntention) {
        List<BusiTenderIntention> list = busiTenderIntentionService.selectList(busiTenderIntention);
        ExcelUtil<BusiTenderIntention> util = new ExcelUtil<BusiTenderIntention>(BusiTenderIntention.class);
        util.exportExcel(response, list, "采购意向数据");
    }

    /**
     * 获取采购意向详细信息
     */
    @PreAuthorize("@ss.hasPermi('tender:intention:query')")
    @ApiOperation(value = "获取采购意向详细信息")
    @ApiImplicitParam(name = "intentionId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{intentionId}")
    public AjaxResult getInfo(@PathVariable("intentionId") Long intentionId) {
      /*  Map<String, String> attachmentMap = new HashMap<>();
        List<BusiAttachment> busiId = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().eq("busi_id", byId.getIntentionId()));
        if (!busiId.isEmpty()) {
            // busiId 不为空，执行相应操作
            byId.setAttachments(busiId);
            for (BusiAttachment attachment : busiId) {
                attachmentMap.put(attachment.getFileType(), attachment.getFilePath());
            }
        }
        byId.setAttachmentMap(attachmentMap);*/
        return busiTenderIntentionService.getBusiTenderIntentionById(intentionId);
    }

    /**
     * 新增采购意向
     */
    @PreAuthorize("@ss.hasPermi('tender:intention:add')")
    @Log(title = "采购意向", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购意向")
    @PostMapping
    public AjaxResult add(@RequestBody BusiTenderIntention busiTenderIntention) {
        busiTenderIntention.setIntentionCode(IdUtils.generateCodeWithPrefix("ZBYX"));
        busiTenderIntention.setTendererId(getEntId());
        busiTenderIntention.setTendererName(getLoginUser().getUser().getDept().getDeptName());
        busiTenderIntention.setCreateBy(getUsername());
        busiTenderIntention.setUpdateBy(getUsername());
        return busiTenderIntentionService.saveBusiTenderIntention(busiTenderIntention);
    }

    /**
     * 修改采购意向
     */
    @PreAuthorize("@ss.hasPermi('tender:intention:edit')")
    @Log(title = "采购意向", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购意向")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiTenderIntention busiTenderIntention) {
        return busiTenderIntentionService.editBusiTenderIntention(busiTenderIntention);
    }

    /**
     * 删除采购意向
     */
    @PreAuthorize("@ss.hasPermi('tender:intention:remove')")
    @Log(title = "采购意向", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购意向")
    @DeleteMapping("/{intentionIds}")
    public AjaxResult remove(@PathVariable Long[] intentionIds) {
        return toAjax(busiTenderIntentionService.removeByIds(Arrays.asList(intentionIds)));
    }


    /**
     * 评审记录文件下载记录
     *
     * @param params
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/downLoadTenderIntentionFile")
    public AjaxResult downLoadTenderIntentionFile(@RequestParam Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {
        System.out.println(request.getRemoteAddr());
        System.out.println(AddressUtils.getRealAddressByIP(request.getRemoteAddr()));
        System.out.println(request.getRemoteAddr());
        return AjaxResult.success();
    }
}
