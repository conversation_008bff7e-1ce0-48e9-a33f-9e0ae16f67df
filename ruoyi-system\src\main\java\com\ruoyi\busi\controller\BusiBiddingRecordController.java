package com.ruoyi.busi.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiBidEvaluation;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiBiddingRecord;
import com.ruoyi.busi.service.IBusiBiddingRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 投标记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "投标记录管理")
@RestController
@RequestMapping("/bidding/record")
public class BusiBiddingRecordController extends BaseController {
    @Autowired
    private IBusiBiddingRecordService busiBiddingRecordService;

    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
/**
 * 查询投标记录列表
 */
//@PreAuthorize("@ss.hasPermi('bidding:record:list')")
@ApiOperation(value = "查询投标记录列表")
@GetMapping("/list")
    public TableDataInfo list(BusiBiddingRecord busiBiddingRecord) {
        startPage();
        List<BusiBiddingRecord> list = busiBiddingRecordService.selectList(busiBiddingRecord);
        return getDataTable(list);
    }

    /**
     * 导出投标记录列表
     */
    @PreAuthorize("@ss.hasPermi('bidding:record:export')")
    @Log(title = "投标记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出投标记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiBiddingRecord busiBiddingRecord) {
        List<BusiBiddingRecord> list = busiBiddingRecordService.selectList(busiBiddingRecord);
        ExcelUtil<BusiBiddingRecord> util = new ExcelUtil<BusiBiddingRecord>(BusiBiddingRecord. class);
        util.exportExcel(response, list, "投标记录数据");
    }

    /**
     * 获取投标记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bidding:record:query')")
    @ApiOperation(value = "获取投标记录详细信息")
    @ApiImplicitParam(name = "biddingId", value = "投标记录id", required = true, dataType = "Long")
    @GetMapping(value = "/{biddingId}")
    public AjaxResult getInfo(@PathVariable("biddingId")Long biddingId) {
        BusiBiddingRecord byId = busiBiddingRecordService.getById(biddingId);
        byId.setAttachments(iBusiAttachmentService.getByBusiId(byId.getBiddingId()));
        return success(byId);
    }
    @ApiOperation(value = "撤回上传投标记录")
    @ApiImplicitParam(name = "biddingId", value = "投标记录id", required = true, dataType = "Long")
    @GetMapping(value = "/getCancelInfo/{biddingId}")
    public AjaxResult getCancelInfo(@PathVariable("biddingId")Long biddingId) {
        BusiBiddingRecord byId = busiBiddingRecordService.getById(biddingId);
        byId.setCancelTime(new Date());
        byId.setUpdateBy(getUsername());
        byId.setUpdateTime(new Date());
//        busiBiddingRecordService.saveOrUpdate(byId);
//        //删除附件
//        List<BusiAttachment> busiAttachments = iBusiAttachmentService.list(new QueryWrapper<BusiAttachment>().eq("busi_id", byId.getBiddingId()).eq("del_flag",0));
//        List<Long> collect = busiAttachments.stream().map(BusiAttachment::getAttachmentId).collect(Collectors.toList());
//        iBusiAttachmentService.removeByIds(collect);
//       // return success(busiBiddingRecordService.removeById(byId));
        return busiBiddingRecordService.getCancelInfo(byId);

    }
    /**
     * 新增投标记录
     */
    @PreAuthorize("@ss.hasPermi('bidding:record:add')")
    @Log(title = "投标记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增投标记录")
    @PostMapping
    public AjaxResult add(@RequestBody BusiBiddingRecord busiBiddingRecord, HttpServletRequest request, HttpServletResponse response) {
        LoginUser loginUser = getLoginUser();
        busiBiddingRecord.setCreateBy(getUsername());
        busiBiddingRecord.setUpdateBy(getUsername());
        busiBiddingRecord.setBidderId(getEntId());
        busiBiddingRecord.setBidderName(loginUser.getUser().getEnt().getEntName());
        busiBiddingRecord.setBidderCode(loginUser.getUser().getEnt().getEntCode());
        busiBiddingRecord.setUploadIp(request.getRemoteAddr());
        busiBiddingRecord.setUploadTime(new Date());
        return busiBiddingRecordService.saveBiddingRecord(busiBiddingRecord);
    }

    /**
     * 修改投标记录
     */
    @PreAuthorize("@ss.hasPermi('bidding:record:edit')")
    @Log(title = "投标记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改投标记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiBiddingRecord busiBiddingRecord) {
        busiBiddingRecord.setUpdateBy(getUsername());
        return toAjax(busiBiddingRecordService.updateById(busiBiddingRecord));
    }

    /**
     * 删除投标记录
     */
    @PreAuthorize("@ss.hasPermi('bidding:record:remove')")
    @Log(title = "投标记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除投标记录")
    @DeleteMapping("/{biddingIds}")
    public AjaxResult remove(@PathVariable Long[] biddingIds) {
        return toAjax(busiBiddingRecordService.removeByIds(Arrays.asList(biddingIds)));
    }

    @ApiOperation(value = "查询投标记录列表")
    @GetMapping("/getList")
    public AjaxResult getList(BusiBiddingRecord busiBiddingRecord) {
        List<BusiBiddingRecord> list = busiBiddingRecordService.selectList(busiBiddingRecord);
        return AjaxResult.success(list);
    }

    //在线解密
    @RequestMapping("/responseFileDecryption")
    public AjaxResult responseFileDecryption(@RequestBody BusiBiddingRecord busiBiddingRecord) throws Exception {

        return   busiBiddingRecordService.responseFileDecryption(busiBiddingRecord,  getLoginUser());
    }

    //获取已经解密的
    @GetMapping(value = "/responseFileDecryptionList/{projectId}")
    public AjaxResult responseFileDecryption(@PathVariable("projectId") Long projectId) throws Exception {
        return   busiBiddingRecordService.responseFileDecryptionList(projectId);
    }

    @GetMapping(value = "/getProjectFileById/{projectId}")
    public AjaxResult getProjectFileById(@PathVariable("projectId") Long projectId) throws Exception {
        return   busiBiddingRecordService.getProjectFileById(projectId);
    }


}
