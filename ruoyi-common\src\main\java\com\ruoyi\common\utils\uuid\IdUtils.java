package com.ruoyi.common.utils.uuid;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ID生成器工具类
 *
 * <AUTHOR>
 */
public class IdUtils
{
    static {
        IdGeneratorOptions options = new IdGeneratorOptions();
        options.Method = 1;
        options.BaseTime = 1582206693000L;
        options.WorkerId = 1;
        options.WorkerIdBitLength = 3;
        options.SeqBitLength = 10;
        YitIdHelper.setIdGenerator(options);
    }
    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID()
    {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID()
    {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 随机UUID
     */
    public static String fastUUID()
    {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID()
    {
        return UUID.fastUUID().toString(true);
    }

    /**
     * 使用yitter的雪花算法生成纯数字id
     */
    public static Long longUUID(){
        return YitIdHelper.nextId();
    }

    public static void main(String[] args) {
        long newId = IdUtils.longUUID();
        System.out.println("=====================================");
        System.out.println("这是用方法 1 生成的 Id：" + newId + "  长度：" + String.valueOf(newId).length());
    }

    /**
     * 生成带有指定前缀的代码，基于当前日期时间。
     *
     * @param prefix 代码的前缀部分
     * @return 完整的代码，格式为：prefix-yyyyMMddHHmmss
     */
    public static String generateCodeWithPrefix(String prefix) {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();

        // 设置日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

        // 格式化日期时间字符串
        String formattedDateTime = now.format(formatter);

        // 生成并返回完整代码
        return prefix + "-" + formattedDateTime;
    }
}
