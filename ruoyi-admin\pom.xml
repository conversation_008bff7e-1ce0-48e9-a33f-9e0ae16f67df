<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.8.7</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>ruoyi-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- 测试所需 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.5.1</version>
        </dependency>
        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>
         <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-generator</artifactId>
        </dependency>

        <!-- 授权验证-->
<!--        <dependency>-->
<!--            <groupId>com.ruoyi</groupId>-->
<!--            <artifactId>yzh-license</artifactId>-->
<!--            <version>${ruoyi.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.ruoyi</groupId>-->
<!--            <artifactId>yzh-license</artifactId>-->
<!--            <version>3.8.7</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.15</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
           </plugin>
            <!--<plugin>
                &lt;!&ndash;
                加密后，方法体被清空，保留方法参数、注解等信息.主要兼容swagger文档注解扫描
                方法体被清空后，反编译只能看到方法名和注解，看不到方法体的具体内容
                加密后的项目需要设置javaagent:来启动，启动过程中解密class,完全内存解密，不留下任何解密后的文件
                启动加密后的jar,生成xXX-encrypted.jar,这个就是加密后的jar文件，加密后不可直接执行
                无密码启动方式，java -javaagent:Xxx-encrypted.jar -jar Xxx-encrypted.jar
                有密码启动方式，java -javaagent:Xxx-encrypted.jan='-pwd=密码' -jar xxx-encrypted.jar
                &ndash;&gt;
                <groupId>net.roseboy</groupId>
                <artifactId>classfinal-maven-plugin</artifactId>
                <version>1.2.1</version>
                <configuration>
                    &lt;!&ndash;#表示启动时不需要密码，事实上对于代码混淆来说，这个密码没什么用，它只是一个启动密码&ndash;&gt;
                    <password>#</password>
                    <excludes>org.spring</excludes>
                    &lt;!&ndash;加密的包名，多个包用逗号分开&ndash;&gt;
                    <packages>${groupId}</packages>
                    &lt;!&ndash;加密的配置文件，多个包用逗号分开&ndash;&gt;
                    <cfgfiles>application.yml,application-*.yml</cfgfiles>
                    &lt;!&ndash;jar包1ib下面要加密的jar依赖文件，多个包用逗号分开&ndash;&gt;
                    &lt;!&ndash;<libjars>yzh-license.jar</libjars>&ndash;&gt;
                    &lt;!&ndash;<code></code>&ndash;&gt;
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>classFinal</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
