package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 专家抽取回避对象 busi_extract_expert_evade
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("专家抽取回避对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiExtractExpertEvadeMapper.BusiExtractExpertEvadeResult")
public class BusiExtractExpertEvade extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 回避条件id
     */
    @ApiModelProperty("回避条件id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long evadeId;
    /**
     * 专家抽取申请id
     */
    @ApiModelProperty("专家抽取申请id")
    @Excel(name = "专家抽取申请id")
    private Long applyId;
    /**
     * 回避名称
     */
    @ApiModelProperty("回避名称")
    @Excel(name = "回避名称")
    private String evadeName;
    /**
     * 回避类型
     */
    @ApiModelProperty("回避类型")
    @Excel(name = "回避类型")
    private Integer evadeType;
    /**
     * 回避原因
     */
    @ApiModelProperty("回避原因")
    @Excel(name = "回避原因")
    private String evadeReason;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
