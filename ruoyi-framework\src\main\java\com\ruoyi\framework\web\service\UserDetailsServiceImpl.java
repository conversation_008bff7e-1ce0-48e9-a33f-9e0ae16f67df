package com.ruoyi.framework.web.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.enums.UserStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.framework.web.domain.ZcxhInfo;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.third.domain.ThirdServerUserBind;
import com.ruoyi.third.service.IThirdServerUserBindService;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private IBaseEntInfoService iBaseEntInfoService;

    @Autowired
    private IThirdServerUserBindService iThirdServerUserBindService;

    @Value("${extractcode.zcxhGetUserInfo}")
    private String zcxhGetUserInfo;
    @Value("${extractcode.thirdPartySecret}")
    private String thirdPartySecret;
    @Value("${extractcode.ssoPassword}")
    private String ssoPassword;
    private static final ThreadLocal<JSONObject> threadLocalData = new ThreadLocal<>();

    public static void setAdditionalData(JSONObject data) {
        threadLocalData.set(data);
    }

    public static void clearAdditionalData() {
        threadLocalData.remove();
    }

    @Transactional
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        AtomicReference<SysUser> user = new AtomicReference<>(userService.selectUserByUserName(username));
           try {
               JSONObject object = threadLocalData.get();
               if(object.containsKey(UserConstants.LOGIN_TYPE_KEY)){
                   String loginTypeKey = object.getString(UserConstants.LOGIN_TYPE_KEY);
                   //删除线程变量
                   threadLocalData.remove();
                   if(loginTypeKey.equals("zcxh")){
                        user.set(syncZcxhSysUser(username, user.get(),null));
                    }else if(loginTypeKey.equals("expert")){
                        //不存在  新增后登录
                       RegisterBody registerBody = new RegisterBody();
                       //创建账户
                       registerBody.setUsername(username);
                       //registerBody.setPassword(username.substring(username.length()-6));
                       registerBody.setPassword(this.ssoPassword);
                       registerBody.setThirdPartySecret(thirdPartySecret);
                       registerBody.setOpUser("expertAutoRegister");
                       registerBody.setType("expert");
                       AjaxResult registerResult = registerService.register(registerBody);
                       if (registerResult.isError()) {
                           throw new ServiceException(registerResult.get("msg")+"");
                       }
                       user.set((SysUser) registerResult.get("user"));
                    }
               }
           }catch (Exception e){
               log.info("第三方账户登录", e.getMessage());
           }

        if (StringUtils.isNull(user.get())) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        } else if (UserStatus.DELETED.getCode().equals(user.get().getDelFlag())) {
            log.info("登录用户：{} 已被删" +
                    "除.", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        } else if (UserStatus.DISABLE.getCode().equals(user.get().getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }

        passwordService.validate(user.get());
        //查询企业
        if (null != user.get().getEntId()) {
            user.get().setEnt(iBaseEntInfoService.getById(user.get().getEntId()));
        }
        return createLoginUser(user.get());
    }

    @Transactional
    public SysUser syncZcxhSysUser(String username, SysUser user, ThirdServerUserBind thirdServerUserBind) {
        //去政府采购协会查询用户
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        //String password = usernamePasswordAuthenticationToken.getCredentials().toString();
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(zcxhGetUserInfo+"?username="+username)
                    .method("GET", null)
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string(); // Read response body as string
                ZcxhInfo zcxhInfo = JSONObject.parseObject(responseBody, ZcxhInfo.class);
                System.out.println("return " + JSONObject.toJSONString(zcxhInfo));
                //采购协会角色。-1 GM，0专家库，2代理机构从业人员 不允许跳转限额以下
                //1, "代理机构"3, "采购单位"4, "供应商"
                //限额一下角色  企业类型，1采购单位2代理机构3供应商,4专家
                RegisterBody registerBody = new RegisterBody();

                if (zcxhInfo.getType()==3){
                    //采购单位
                    registerBody.setEntType(1);
                    //创建账户
                    ZcxhInfo.AgencyBean agency = zcxhInfo.getAgency();
                    registerBody.setUsername(username);
                    registerBody.setPassword(this.ssoPassword);
                    registerBody.setThirdPartySecret(thirdPartySecret);
                    registerBody.setEntName(agency.getDwmc());
                    registerBody.setEntCode(agency.getTyshxydm());
                    registerBody.setType("zcxh");
                    registerBody.setEntAddress(agency.getQyszd());
                    registerBody.setEntKeyAgencyAreas(agency.getZddlly());
                    registerBody.setOpUser("syncZcxh");
                    AjaxResult registerResult = registerService.register(registerBody);
                    if (registerResult.isError()) {
                        throw new ServiceException(registerResult.get("msg")+"");
                    }
                    user = (SysUser) registerResult.get("user");
                    thirdServerUserBind = new ThirdServerUserBind();

                    thirdServerUserBind.setServerUserId(user.getUserId());
                    thirdServerUserBind.setServerName("zcxh");
                    thirdServerUserBind.setLocalUserId(user.getUserId());
                    thirdServerUserBind.getParams().put("opUser","sync");
                    iThirdServerUserBindService.saveOrUpdate(thirdServerUserBind);
                }
                if (zcxhInfo.getType()==0){
                    //专家
                    registerBody.setEntType(0);
                    //创建账户
                    ZcxhInfo.ExpertBean expert = zcxhInfo.getExpert();
                    registerBody.setUsername(username);
                    registerBody.setPassword(this.ssoPassword);
                    registerBody.setThirdPartySecret(thirdPartySecret);
                    registerBody.setEntName(expert.getXm());
                    registerBody.setEntCode(expert.getZjhm());
                    registerBody.setType("zcxh");
                    registerBody.setEntAddress(expert.getTxdz());
                    registerBody.setEntKeyAgencyAreas(expert.getPspm());
                    registerBody.setOpUser("syncZcxh");
                    AjaxResult registerResult = registerService.register(registerBody);
                    if (registerResult.isError()) {
                        throw new ServiceException(registerResult.get("msg")+"");
                    }
                    user = (SysUser) registerResult.get("user");
                    thirdServerUserBind = new ThirdServerUserBind();

                    thirdServerUserBind.setServerUserId(user.getUserId());
                    thirdServerUserBind.setServerName("zcxh");
                    thirdServerUserBind.setLocalUserId(user.getUserId());
                    thirdServerUserBind.getParams().put("opUser","sync");
                    iThirdServerUserBindService.saveOrUpdate(thirdServerUserBind);
                }
                if (zcxhInfo.getType()==1){
                    registerBody.setEntType(2);
                    //创建账户
                    ZcxhInfo.AgencyBean agency = zcxhInfo.getAgency();
                    registerBody.setUsername(username);
                    registerBody.setPassword(this.ssoPassword);
                    registerBody.setThirdPartySecret(thirdPartySecret);
                    registerBody.setEntName(agency.getDwmc());
                    registerBody.setEntCode(agency.getTyshxydm());
                    registerBody.setType("zcxh");
                    registerBody.setEntAddress(agency.getQyszd());
                    registerBody.setEntKeyAgencyAreas(agency.getZddlly());
                    registerBody.setOpUser("syncZcxh");
                    AjaxResult registerResult = registerService.register(registerBody);
                    if (registerResult.isError()) {
                        throw new ServiceException(registerResult.get("msg")+"");
                    }
                    user = (SysUser) registerResult.get("user");
                    thirdServerUserBind = new ThirdServerUserBind();

                    thirdServerUserBind.setServerUserId(user.getUserId());
                    thirdServerUserBind.setServerName("zcxh");
                    thirdServerUserBind.setLocalUserId(user.getUserId());
                    thirdServerUserBind.getParams().put("opUser","sync");
                    iThirdServerUserBindService.saveOrUpdate(thirdServerUserBind);
                }
                if (zcxhInfo.getType()==4){
                    //供应商
                    registerBody.setEntType(3);
                    //创建账户
                    ZcxhInfo.SupplierBean supplier = zcxhInfo.getSupplier();
                    registerBody.setUsername(username);
                    registerBody.setPassword(this.ssoPassword);
                    registerBody.setThirdPartySecret(thirdPartySecret);
                    registerBody.setEntName(supplier.getName());

                    registerBody.setBusinessLicense(supplier.getBusinessLicenseFile());
                    registerBody.setEntLegalPerson(supplier.getLegalPerson());
                    registerBody.setEntLegalPersonPhone(supplier.getLegalPersonPhone());
                    registerBody.setLegalPersonCardFile(supplier.getLegalPersonCardFile());
                    registerBody.setEntLinkman(supplier.getContactsName());
                    registerBody.setEntContactPhone(supplier.getContactsPhone());

                    registerBody.setEntCode(supplier.getCreditCode());
                    registerBody.setType("zcxh");
                   // registerBody.setEntAddress(supplier.get());
                   // registerBody.setEntKeyAgencyAreas(agency.getZddlly());
                    registerBody.setOpUser("syncZcxh");
                    AjaxResult registerResult = registerService.register(registerBody);
                    if (registerResult.isError()) {
                        throw new ServiceException(registerResult.get("msg")+"");
                    }
                    user = (SysUser) registerResult.get("user");
                    thirdServerUserBind = new ThirdServerUserBind();

                    thirdServerUserBind.setServerUserId(user.getUserId());
                    thirdServerUserBind.setServerName("zcxh");
                    thirdServerUserBind.setLocalUserId(user.getUserId());
                    thirdServerUserBind.getParams().put("opUser","sync");

                }

            } else {
                System.out.println("Failed to post data: " + response.code());
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return user;
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user.getEntId(), user, permissionService.getMenuPermission(user));
    }



}
