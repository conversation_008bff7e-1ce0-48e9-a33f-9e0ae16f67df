package com.ruoyi.base.controller;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.base.vo.DayType;
import com.ruoyi.base.vo.Days;
import com.ruoyi.base.vo.SiteOccupationVO;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import com.ruoyi.busi.service.IBusiVenueOccupyService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.base.domain.BaseVenueInfo;
import com.ruoyi.base.service.IBaseVenueInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 场地信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "场地信息管理")
@RestController
@RequestMapping("/venue/info")
public class BaseVenueInfoController extends BaseController {
    @Autowired
    private IBaseVenueInfoService baseVenueInfoService;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;
/**
 * 查询场地信息列表
 */
//@PreAuthorize("@ss.hasPermi('venue:info:list')")
@ApiOperation(value = "查询场地信息列表")
@GetMapping("/list")
    public TableDataInfo list(BaseVenueInfo baseVenueInfo) {
        startPage();
        baseVenueInfo.setDelFlag(0);
        List<BaseVenueInfo> list = baseVenueInfoService.selectList(baseVenueInfo);
        return getDataTable(list);
    }

    /**
     * 导出场地信息列表
     */
    @PreAuthorize("@ss.hasPermi('venue:info:export')")
    @Log(title = "场地信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出场地信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseVenueInfo baseVenueInfo) {
        List<BaseVenueInfo> list = baseVenueInfoService.selectList(baseVenueInfo);
        ExcelUtil<BaseVenueInfo> util = new ExcelUtil<BaseVenueInfo>(BaseVenueInfo. class);
        util.exportExcel(response, list, "场地信息数据");
    }

    /**
     * 获取场地信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('venue:info:query')")
    @ApiOperation(value = "获取场地信息详细信息")
    @ApiImplicitParam(name = "venueId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{venueId}")
    public AjaxResult getInfo(@PathVariable("venueId")Long venueId) {
        return success(baseVenueInfoService.getById(venueId));
    }

    /**
     * 新增场地信息
     */
    @PreAuthorize("@ss.hasPermi('venue:info:add')")
    @Log(title = "场地信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增场地信息")
    @PostMapping
    public AjaxResult add(@RequestBody BaseVenueInfo baseVenueInfo) {
        baseVenueInfo.setDelFlag(0);
        baseVenueInfo.setCreateBy(getUsername());
        baseVenueInfo.setUpdateBy(getUsername());
        return toAjax(baseVenueInfoService.save(baseVenueInfo));
    }
    //    @PreAuthorize("@ss.hasPermi('venue:info:getSiteOccupationList')")
    @ApiOperation(value = "查询开标/评标场地占用列表")
    @GetMapping("/getSiteOccupationList")
    public AjaxResult getSiteOccupationList(@RequestParam(defaultValue = "1") String venueType) {
      /*  //获取全部场地
        List<SiteOccupationVO> siteOccupationVOS=new ArrayList<>();
        BaseVenueInfo baseVenueInfo=new BaseVenueInfo();
        baseVenueInfo.setVenueType(venueType);
        List<BaseVenueInfo> list = baseVenueInfoService.list(new QueryWrapper<BaseVenueInfo>().eq("venue_type",venueType));

        for (BaseVenueInfo venueInfo : list) {
            SiteOccupationVO siteOccupationVO=new SiteOccupationVO();
            List<BusiVenueOccupy> busiVenueOccupies=new ArrayList<>();
            QueryWrapper<BusiVenueOccupy> queryWrapper=new QueryWrapper<BusiVenueOccupy>();
            queryWrapper.eq("venue_id",venueInfo.getVenueId());
            queryWrapper.eq("venue_type",venueType);
            siteOccupationVO.setVenueId(venueInfo.getVenueId());
            siteOccupationVO.setVenueName(venueInfo.getVenueName());
            //获取最近15天
            List<String> fifteenDays=get15Days();
            queryWrapper.between("create_time", fifteenDays.get(0), fifteenDays.get(fifteenDays.size()-1));

            busiVenueOccupies = busiVenueOccupyService.list(queryWrapper);

            // 创建SimpleDateFormat对象，用于格式化日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 遍历days列表
            List<Days> daysvo=new ArrayList<>();
            for (String day : fifteenDays) {
                Days days=new Days();
                days.setDate(day);
                List<DayType> dayTypes=new ArrayList<>();
                dayTypes.add(new DayType(0,0));
                dayTypes.add(new DayType(1,0));
                dayTypes.add(new DayType(2,0));
                days.setDayTypeList(dayTypes);
                // 获取日期等于day 的占用记录
                List<BusiVenueOccupy> filteredList = busiVenueOccupies.stream()
                        .filter(busiVenueOccupy -> {
                            // 获取busiVenueOccupy的createTime并格式化为字符串
                            String createTimeStr = dateFormat.format(busiVenueOccupy.getCreateTime());
                            // 比较格式化后的createTime与str是否相等
                            return day.equals(createTimeStr);
                        })
                        .collect(Collectors.toList());
                // 检查filteredList是否不为null且不为空
                if (filteredList != null && !filteredList.isEmpty()) {
                    long zeroCount = filteredList.stream().filter(e -> e.getBidEvaluationPeriod().equals(0l)).count();
                    long oneCount = filteredList.stream().filter(e -> e.getBidEvaluationPeriod().equals(1l)).count();
                    long twoCount = filteredList.stream().filter(e -> e.getBidEvaluationPeriod().equals(2l)).count();
                    dayTypes.stream().filter(e->e.getType().equals(0L)).findFirst().orElse(null).setStatus(zeroCount > 0 ? 1 : 0);
                    dayTypes.stream().filter(e->e.getType().equals(1L)).findFirst().orElse(null).setStatus(oneCount > 0 ? 1 : 0);
                    dayTypes.stream().filter(e->e.getType().equals(2L)).findFirst().orElse(null).setStatus(twoCount > 0 ? 1 : 0);
                }
                daysvo.add(days);
                siteOccupationVO.setDayList(daysvo);
            }

            siteOccupationVOS.add(siteOccupationVO);
        }*/
        return AjaxResult.success(baseVenueInfoService.getSiteOccupationList(venueType));
    }


    List<String>  get15Days(){
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 初始化日期列表
        List<String> dates = new ArrayList<>();

        // 循环生成未来15天的日期
        for (int i = 0; i <= 14; i++) {
            // 计算日期
            LocalDate date = today.plusDays(i);
            // 将日期格式化为字符串并添加到列表中
            String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dates.add(formattedDate);
        }
        return dates;
    }

    /**
     * 修改场地信息
     */
    @PreAuthorize("@ss.hasPermi('venue:info:edit')")
    @Log(title = "场地信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改场地信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BaseVenueInfo baseVenueInfo) {
        baseVenueInfo.setUpdateBy(getUsername());
        return toAjax(baseVenueInfoService.updateById(baseVenueInfo));
    }

    /**
     * 删除场地信息
     */
    @PreAuthorize("@ss.hasPermi('venue:info:remove')")
    @Log(title = "场地信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除场地信息")
    @DeleteMapping("/{venueIds}")
    public AjaxResult remove(@PathVariable Long[] venueIds) {
        return toAjax(baseVenueInfoService.removeByIds(Arrays.asList(venueIds)));
    }
}
