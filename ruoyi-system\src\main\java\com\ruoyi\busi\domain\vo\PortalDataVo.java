package com.ruoyi.busi.domain.vo;

import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.RoundingMode;
import java.util.Date;

@Data
public class PortalDataVo {

    /**
     * 数据类型
     */
    private Integer dataType;
    /**
     * 数据类型名称
     */
    private String dataTypeName;


    //项目相关内容
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 预算金额
     */
    private String budgetAmount;
    /**
     * 发布时间
     */
    private Date projectTime;
    /**
     * 采购方式
     */
    private String tenderMode;
    private String tenderModeName;
    private String projectIndustry;

    //主体内容
    /**
     * 主键id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 内容
     */
    private String content;
    /**
     * 类型
     */
    private String type;
    /**
     * 发布时间
     */
    private Date releaseTime;
    /**
     * 发布时间
     */
    private Date bidOpeningTime;

    //采购人信息
    /**
     * 采购人名称
     */
    private String tendererName;
    /**
     * 采购人联系人
     */
    private String tendererContactPerson;
    /**
     * 采购人联系方式
     */
    private String tendererPhone;

    //代理信息
    /**
     * 代理机构名称
     */
    private String agencyName;
    /**
     * 代理机构联系人
     */
    private String agencyContactPerson;
    /**
     * 代理机构联系方式
     */
    private String agencyPhone;

    //代理信息
    /**
     * 中标人名称
     */
    private String bidderName;
    private String bidAmount;


    //查询用字段
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    private Integer delFlag;

    public static PortalDataVo initTenderNotice4List(BusiTenderNotice notice){
        PortalDataVo vo = new PortalDataVo();

        vo.setId(notice.getNoticeId());
        vo.setName(notice.getNoticeName());
        vo.setCode(notice.getNoticeCode());
        vo.setProjectId(notice.getProjectId());
        vo.setProjectName(notice.getProject().getProjectName());
        vo.setProjectCode(notice.getProject().getProjectCode());
        vo.setTenderMode(notice.getProject().getTenderMode());
        vo.setTenderModeName(notice.getProject().getTenderModeName());
        vo.setProjectIndustry(notice.getProject().getProjectIndustryName());
        vo.setTendererName(notice.getProject().getTendererName());
        vo.setReleaseTime(notice.getNoticeStartTime());
        vo.setBidOpeningTime(notice.getBidOpeningTime());
        vo.setBudgetAmount(String.valueOf(notice.getProject().getBudgetAmount().setScale(2, RoundingMode.HALF_UP)));
        return vo;
    }
    public static PortalDataVo initWinningNotice4List(BusiWinningBidderNotice notice){
        PortalDataVo vo = new PortalDataVo();

        vo.setId(notice.getNoticeId());
        vo.setName(notice.getNoticeName());
        vo.setCode(notice.getNoticeCode());
        vo.setProjectId(notice.getProjectId());
        vo.setProjectName(notice.getProject().getProjectName());
        vo.setProjectCode(notice.getProject().getProjectCode());
        vo.setTenderMode(notice.getProject().getTenderMode());
        vo.setTenderModeName(notice.getProject().getTenderModeName());
        vo.setTendererName(notice.getProject().getTendererName());
        vo.setReleaseTime(notice.getNoticeStartTime());
        return vo;
    }

    public static PortalDataVo initTenderNotice4Info(BusiTenderNotice notice){
        PortalDataVo vo = new PortalDataVo();

        vo.setId(notice.getNoticeId());
        vo.setName(notice.getNoticeName());
        vo.setCode(notice.getNoticeCode());
        vo.setProjectId(notice.getProjectId());
        vo.setProjectName(notice.getProject().getProjectName());
        vo.setProjectCode(notice.getProject().getProjectCode());
        vo.setTenderMode(notice.getProject().getTenderMode());
        vo.setTenderModeName(notice.getProject().getTenderModeName());
        vo.setTendererName(notice.getProject().getTendererName());
        vo.setReleaseTime(notice.getNoticeStartTime());
        vo.setContent(notice.getNoticeContent());
        return vo;
    }

    public static PortalDataVo initWinningNotice4Info(BusiWinningBidderNotice notice){
        PortalDataVo vo = new PortalDataVo();

        vo.setId(notice.getNoticeId());
        vo.setName(notice.getNoticeName());
        vo.setCode(notice.getNoticeCode());
        vo.setProjectId(notice.getProjectId());
        vo.setProjectName(notice.getProject().getProjectName());
        vo.setProjectCode(notice.getProject().getProjectCode());
        vo.setTenderMode(notice.getProject().getTenderMode());
        vo.setTenderModeName(notice.getProject().getTenderModeName());
        vo.setTendererName(notice.getProject().getTendererName());
        vo.setReleaseTime(notice.getNoticeStartTime());
        vo.setContent(notice.getNoticeContent());
        return vo;
    }

    public BusiTenderNotice initNoticeQuery(){
        BusiTenderNotice query = new BusiTenderNotice();
        if (this.getId() != null) { query.setNoticeId(this.getId()); }
        if (this.getProjectId() != null) { query.setProjectId(this.getProjectId()); }
        if (StringUtils.isNoneBlank(this.getName())) { query.setNoticeName(this.getName()); }
        if(StringUtils.isNoneBlank(this.getStartTime()) && StringUtils.isNoneBlank(this.getEndTime())){
            query.getParams().put("beginNoticeStartTime", this.getStartTime());
            query.getParams().put("endNoticeStartTime", this.getEndTime());
        }
//        query.setNoticeStats(1);
        if(this.getDelFlag()!=null){
            query.setDelFlag(this.getDelFlag());
        }else{
            query.setDelFlag(0);
        }
        query.getParams().put("isScope", "false");
        return query;
    }

    public BusiTenderNotice initChangeNoticeQuery(){
        BusiTenderNotice query = new BusiTenderNotice();
        if (this.getProjectId() != null) { query.setProjectId(this.getProjectId()); }
        if (StringUtils.isNoneBlank(this.getName())) { query.setNoticeName(this.getName()); }
        if(StringUtils.isNoneBlank(this.getStartTime()) && StringUtils.isNoneBlank(this.getEndTime())){
            query.getParams().put("beginNoticeStartTime", this.getStartTime());
            query.getParams().put("endNoticeStartTime", this.getEndTime());
        }
        query.setNoticeStats(2);
        query.setDelFlag(0);
        query.getParams().put("isScope", "false");
        return query;
    }

    public BusiWinningBidderNotice initWinningNoticeQuery(){
        BusiWinningBidderNotice query = new BusiWinningBidderNotice();
        if (this.getProjectId() != null) { query.setProjectId(this.getProjectId()); }
        if (this.getId() != null) { query.setNoticeId(this.getId()); }
        if (StringUtils.isNoneBlank(this.getName())) { query.setNoticeName(this.getName()); }
        if(StringUtils.isNoneBlank(this.getStartTime()) && StringUtils.isNoneBlank(this.getEndTime())){
            query.getParams().put("beginCreateTime", this.getStartTime());
            query.getParams().put("endCreateTime", this.getEndTime());
        }
        query.getParams().put("isScope", "false");
        query.setDelFlag(0);
        return query;
    }

    public BusiTenderNotice initCancelNoticeQuery(){
        BusiTenderNotice query = new BusiTenderNotice();
        if (this.getProjectId() != null) { query.setProjectId(this.getProjectId()); }
        if (StringUtils.isNoneBlank(this.getProjectName())) { query.setProjectName(this.getProjectName()); }
        if (StringUtils.isNoneBlank(this.getStartTime())) { query.setProjectName(this.getProjectName()); }
        if(StringUtils.isNoneBlank(this.getStartTime()) && StringUtils.isNoneBlank(this.getEndTime())){
            query.getParams().put("beginNoticeStartTime", this.getStartTime());
            query.getParams().put("endNoticeStartTime", this.getEndTime());
        }
        query.setDelFlag(0);
        query.getParams().put("isScope", "false");
        return query;
    }
}
