import com.alibaba.fastjson2.JSON;
import com.ruoyi.RuoYiApplication;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.procurement.domain.ProcurementDocumentsUinfo;
import com.ruoyi.procurement.service.IProcurementDocumentsUinfoService;
import com.ruoyi.utils.UpdateZhuanJiaInfoParams;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = RuoYiApplication.class)
public class RuoYiTest {

    @Autowired
    IProcurementDocumentsUinfoService iProcurementDocumentsUinfoService;


    @Test
    public void testProdProjectXml() {
        ProcurementDocumentsUinfo queryUinfo = new ProcurementDocumentsUinfo();
        queryUinfo.setEntFileId(1167719171179525L);
        queryUinfo.getParams().put("downItemListName", "项目基本信息,开标一览表,评分办法");
//        AjaxResult ajaxResult = iProcurementDocumentsUinfoService.generateProjectFileZip(queryUinfo);
//        System.out.println(ajaxResult);
    }

    // @Test
    public void contextLoads() {
        // 一般用于检查应用程序的上下文（context）是否加载成功
        System.out.println("RuoYiTest contextLoads");
        //查询省政府采购网数据
        //创建字典类型
        //新增字典数据
    }

    public static void main(String[] args) {
        String json = "{\"id\":388,\"xm\":\"郭秋喜\",\"sjhm\":\"15803723500\",\"zjhm\":\"410511197909140019\", \"khh\": \"中原银行\",\"yhzh\": \"6231189700000170236\"}";
        UpdateZhuanJiaInfoParams updateZhuanJiaInfoParams = JSON.parseObject(json, UpdateZhuanJiaInfoParams.class);

        System.out.println(JSON.toJSONString(updateZhuanJiaInfoParams));
    }

}
