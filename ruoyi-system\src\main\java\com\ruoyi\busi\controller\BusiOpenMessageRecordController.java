package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiOpenMessageRecord;
import com.ruoyi.busi.service.IBusiOpenMessageRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 在线开标消息记录Controller
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Api(tags = "在线开标消息记录管理")
@RestController
@RequestMapping("/message/record")
public class BusiOpenMessageRecordController extends BaseController {
    @Autowired
    private IBusiOpenMessageRecordService busiOpenMessageRecordService;

/**
 * 查询在线开标消息记录列表
 */
@PreAuthorize("@ss.hasPermi('message:record:list')")
@ApiOperation(value = "查询在线开标消息记录列表")
@GetMapping("/list")
    public TableDataInfo list(BusiOpenMessageRecord busiOpenMessageRecord) {
        startPage();
        List<BusiOpenMessageRecord> list = busiOpenMessageRecordService.selectList(busiOpenMessageRecord);
        return getDataTable(list);
    }

    /**
     * 导出在线开标消息记录列表
     */
    @PreAuthorize("@ss.hasPermi('message:record:export')")
    @Log(title = "在线开标消息记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出在线开标消息记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiOpenMessageRecord busiOpenMessageRecord) {
        List<BusiOpenMessageRecord> list = busiOpenMessageRecordService.selectList(busiOpenMessageRecord);
        ExcelUtil<BusiOpenMessageRecord> util = new ExcelUtil<BusiOpenMessageRecord>(BusiOpenMessageRecord. class);
        util.exportExcel(response, list, "在线开标消息记录数据");
    }

    /**
     * 获取在线开标消息记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('message:record:query')")
    @ApiOperation(value = "获取在线开标消息记录详细信息")
    @ApiImplicitParam(name = "id", value = "消息id", required = true, dataType = "Long")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")Long id) {
        return success(busiOpenMessageRecordService.getById(id));
    }

    /**
     * 新增在线开标消息记录
     */
    @PreAuthorize("@ss.hasPermi('message:record:add')")
    @Log(title = "在线开标消息记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增在线开标消息记录")
    @PostMapping
    public AjaxResult add(@RequestBody BusiOpenMessageRecord busiOpenMessageRecord) {
        return toAjax(busiOpenMessageRecordService.save(busiOpenMessageRecord));
    }

    /**
     * 修改在线开标消息记录
     */
    @PreAuthorize("@ss.hasPermi('message:record:edit')")
    @Log(title = "在线开标消息记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改在线开标消息记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiOpenMessageRecord busiOpenMessageRecord) {
        return toAjax(busiOpenMessageRecordService.updateById(busiOpenMessageRecord));
    }

    /**
     * 删除在线开标消息记录
     */
    @PreAuthorize("@ss.hasPermi('message:record:remove')")
    @Log(title = "在线开标消息记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除在线开标消息记录")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(busiOpenMessageRecordService.removeByIds(Arrays.asList(ids)));
    }
    //查询指定项目开标时的聊天记录
    @RequestMapping ("/historyMessages/{projectId}")
    public AjaxResult list(@PathVariable Long projectId) {
        return busiOpenMessageRecordService.historyMessages(projectId);
    }

}
