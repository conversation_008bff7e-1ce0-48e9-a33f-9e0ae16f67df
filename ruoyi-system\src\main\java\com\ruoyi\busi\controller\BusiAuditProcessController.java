package com.ruoyi.busi.controller;

import com.ruoyi.common.core.domain.entity.BusiAuditProcess;
import com.ruoyi.busi.service.IBusiAuditProcessService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审核流程管理Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "审核流程管理")
@RestController
@RequestMapping("/auditProcess")
public class BusiAuditProcessController extends BaseController {
    @Autowired
    private IBusiAuditProcessService auditProcessService;

/**
 * 查询附件列表
 */
@ApiOperation(value = "查询审核流程")
@GetMapping("/listByBusi/{busiId}")
    public AjaxResult listByBusi(@PathVariable("busiId")Long busiId) {
        List<BusiAuditProcess> list = auditProcessService.selectList(busiId);
        return AjaxResult.success(list);
    }

}
