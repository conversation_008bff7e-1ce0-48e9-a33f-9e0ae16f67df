package com.ruoyi.busi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiBidOpening;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.service.IBusiBidOpeningService;
import com.ruoyi.busi.service.IBusiBidderInfoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 参与投标人信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "参与投标人信息管理")
@RestController
@RequestMapping("/bidding/info")
public class BusiBidderInfoController extends BaseController {
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiBidOpeningService iBusiBidOpeningService;
/**
 * 查询参与投标人信息列表
 */
//@PreAuthorize("@ss.hasPermi('bidding:info:list')")
@ApiOperation(value = "查询参与投标人信息列表")
@GetMapping("/list")
    public TableDataInfo list(BusiBidderInfo busiBidderInfo) {
        startPage();
        List<BusiBidderInfo> list = busiBidderInfoService.selectList(busiBidderInfo);
        return getDataTable(list);
    }

    /**
     * 导出参与投标人信息列表
     */
    @PreAuthorize("@ss.hasPermi('bidding:info:export')")
    @Log(title = "参与投标人信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出参与投标人信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiBidderInfo busiBidderInfo) {
        List<BusiBidderInfo> list = busiBidderInfoService.selectList(busiBidderInfo);
        ExcelUtil<BusiBidderInfo> util = new ExcelUtil<BusiBidderInfo>(BusiBidderInfo. class);
        util.exportExcel(response, list, "参与投标人信息数据");
    }

    /**
     * 获取参与投标人信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('bidding:info:query')")
    @ApiOperation(value = "获取参与投标人信息详细信息")
    @ApiImplicitParam(name = "bidderId", value = "投标人id", required = true, dataType = "Long")
    @GetMapping(value = "/{bidderId}")
    public AjaxResult getInfo(@PathVariable("bidderId")Long bidderId) {
        return success(busiBidderInfoService.getById(bidderId));
    }

    /**
     * 新增参与投标人信息
     */
    @PreAuthorize("@ss.hasPermi('bidding:info:add')")
    @Log(title = "参与投标人信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增参与投标人信息")
    @PostMapping
    public AjaxResult add(@RequestBody BusiBidderInfo busiBidderInfo) {
        busiBidderInfo.setCreateBy(getUsername());
        busiBidderInfo.setUpdateBy(getUsername());
        return toAjax(busiBidderInfoService.save(busiBidderInfo));
    }
    @PreAuthorize("@ss.hasPermi('bidding:info:add')")
    @Log(title = "参与投标人信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增参与投标人信息")
    @PostMapping(value = "addBatch")
    public AjaxResult addBatch(@RequestBody List< BusiBidderInfo> busiBidderInfos) {
        for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
            busiBidderInfo.setCreateBy(getUsername());
            busiBidderInfo.setUpdateBy(getUsername());
        }
        return toAjax(busiBidderInfoService.saveBatch(busiBidderInfos));
    }


    /**
     * 修改参与投标人信息
     */
    @PreAuthorize("@ss.hasPermi('bidding:info:edit')")
    @Log(title = "参与投标人信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改参与投标人信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiBidderInfo busiBidderInfo) {
        busiBidderInfo.setUpdateBy(getUsername());
        return toAjax(busiBidderInfoService.updateById(busiBidderInfo));
    }

    @PostMapping("/updateBidderInfo")
    public AjaxResult updateBidderInfo(@RequestBody BusiBidderInfo busiBidderInfo) throws Exception {
        busiBidderInfo.setUpdateBy(getUsername());
        return busiBidderInfoService.updateBidderInfo(busiBidderInfo,getLoginUser());
    }



    /**
     * 删除参与投标人信息
     */
    @PreAuthorize("@ss.hasPermi('bidding:info:remove')")
    @Log(title = "参与投标人信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除参与投标人信息")
    @DeleteMapping("/{bidderIds}")
    public AjaxResult remove(@PathVariable Long[] bidderIds) {
        return toAjax(busiBidderInfoService.removeByIds(Arrays.asList(bidderIds)));
    }


    /**
     *线上开标，供应商签到接口
     */
    @ApiOperation(value = "线上开标，供应商签到接口")
    @PostMapping(value = "/signIn")
    public AjaxResult signIn(@RequestBody BusiBidderInfo busiBidderInfo) {
        LoginUser loginUser = getLoginUser();
       return busiBidderInfoService.signIn(loginUser,busiBidderInfo);

    }

    /**
     *线上开标，投标人公示接口
     */
    @ApiOperation(value = "线上开标，投标人公示接口")
    @GetMapping (value = "/bidderAnnouncement/{projectId}")
    public AjaxResult bidderAnnouncement(@PathVariable("projectId")Long projectId) {
        return busiBidderInfoService.bidderAnnouncement(projectId);
    }


    @ApiOperation(value = "新增成交结果")
    @GetMapping (value = "/getBidderInfoByProjectID/{projectId}")
    public AjaxResult getBidderInfoByProjectID(@PathVariable("projectId")Long projectId) {
        return busiBidderInfoService.getBidderInfoByProjectID(projectId);
    }
    //导出开标记录
    @GetMapping (value = "/exportBidOpeningRecords")
    public AjaxResult exportBidOpeningRecords(Long projectId) throws IOException {
        BusiBidOpening busiBidOpening = iBusiBidOpeningService.selectByProject(projectId);
        // 使用Stream API过滤fileType值为1的BusiAttachment对象
        if(busiBidOpening.getAttachments().size()>0){
            List<BusiAttachment> filteredAttachments = busiBidOpening.getAttachments().stream()
                    .filter(attachment -> attachment.getFileType() .equals("1") )
                    .collect(Collectors.toList());
            busiBidOpening.setAttachments(filteredAttachments);
        }
        return AjaxResult.success(busiBidOpening);
//        busiBidderInfoService.exportBidOpeningRecords(projectId);
   }


}
