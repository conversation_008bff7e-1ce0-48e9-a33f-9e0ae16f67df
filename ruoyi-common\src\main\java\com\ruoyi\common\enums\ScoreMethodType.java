package com.ruoyi.common.enums;

/**
 * 评分办法类别
 * 
 * <AUTHOR>
 */
public enum ScoreMethodType
{
    ITEM_CODE_ZGXPS("zgxps", "资格性评审"),
    ITEM_CODE_JSBPS("jsbps", "技术标评审"),
    ITEM_CODE_SWBPS("swbps", "商务标评审"),
    ITEM_CODE_FHBPS("fhbps", "符合性评审"),
    ITEM_CODE_TBBJDF("tbbjdf", "投标报价打分"),
    ITEM_CODE_PSHZQR("pshzqr", "评审汇总确认");

    private final String code;
    private final String info;

    ScoreMethodType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
