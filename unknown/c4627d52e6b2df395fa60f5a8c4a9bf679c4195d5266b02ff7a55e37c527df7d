package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 评标记录对象 busi_bid_evaluation
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ApiModel("评标记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiBidEvaluationMapper.BusiBidEvaluationResult")
public class BusiBidEvaluation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评标记录id
     */
    @ApiModelProperty("评标记录id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long bidEvaluationId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 评审开始时间
     */
    @ApiModelProperty("评审开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "评审开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date evaluationStartTime;
    /**
     * 评审结束时间
     */
    @ApiModelProperty("评审结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "评审结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bidEvaluationEndTime;
    /**
     * 删除标记 0正常 1删除
     */
    @ApiModelProperty("删除标记 0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    @TableField(exist = false)
    List<BusiBidderInfo> projectInfoList;
    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<BusiAttachment> attachments;


    @TableField(exist = false)
    private BusiTenderProject project;
    /*
    是否流标 0否1是
     */
    @TableField(exist = false)
    private Integer abortiveTender;
    public Long getId(){
        return this.bidEvaluationId;
    }
}
