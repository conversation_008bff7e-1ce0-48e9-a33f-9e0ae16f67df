package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * 开标记录对象 busi_bid_opening
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ApiModel("开标记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiBidOpeningMapper.BusiBidOpeningResult")
public class BusiBidOpening extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 投标记录id
     */
    @ApiModelProperty("投标记录id")
        @TableId(type = IdType.ASSIGN_ID)
    private Long bidOpeningId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
            @Excel(name = "项目id")
    private Long projectId;
    /**
     * 项目代码
     */
    @ApiModelProperty("项目代码")
    @Excel(name = "项目代码")
    private String projectCode;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 主持人id
     */
    @ApiModelProperty("主持人id")
            @Excel(name = "主持人id")
    private Long compereId;
    /**
     * 主持人名称
     */
    @ApiModelProperty("主持人名称")
            @Excel(name = "主持人名称")
    private String compereName;
    /**
     * 主持人代码
     */
    @ApiModelProperty("主持人代码")
            @Excel(name = "主持人代码")
    private String compereCode;
    /**
     * 开标方式，0线下，1线上
     */
    @ApiModelProperty("开标方式，0线下，1线上")
            @Excel(name = "开标方式，0线下，1线上")
    private Integer bidOpeningMode;
    /**
     * 解密开始时间
     */
    @ApiModelProperty("解密开始时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Excel(name = "解密开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date decodeStartTime;
    /**
     * 唱标时间
     */
    @ApiModelProperty("唱标时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Excel(name = "唱标时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidAnnounceTime;
    /**
     * 开标时间
     */
    @ApiModelProperty("开标时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Excel(name = "开标时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidOpeningTime;
    /**
     * 开标结束时间
     */
    @ApiModelProperty("开标结束时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Excel(name = "开标结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidOpeningEndTime;
    /**
     * 删除标记，0正常，1删除
     */
    @ApiModelProperty("删除标记，0正常，1删除")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist =  false)
    private List<BusiBidderInfo> busiBidderInfoList = new ArrayList<>();
    @TableField(exist =  false)
    private List<BusiAttachment> attachments = new ArrayList<>();
        }
