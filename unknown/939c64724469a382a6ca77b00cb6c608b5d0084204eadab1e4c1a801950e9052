package com.ruoyi.web.controller.system;

import java.util.*;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.UserDetailsServiceImpl;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.system.service.ISysMenuService;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Log4j2
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;
    @Autowired
    private ISysRoleService sysRoleService;



    @Autowired
    private SysPermissionService permissionService;

    @Value("${extractcode.thirdPartySecret}")
    private String thirdPartySecret;
    @Value("${extractcode.ssoPassword}")
    private String ssoPassword;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/expertLogin")
    public AjaxResult expertLogin(@RequestBody LoginBody loginBody)
    {
        if(loginBody.getUsername().length()<15){
            return AjaxResult.error("请检查身份证号是否输入错误");
        }
        loginBody.setThirdPartySecret(thirdPartySecret);
        //loginBody.setPassword(loginBody.getUsername().substring(loginBody.getUsername().length()-6));
        loginBody.setPassword(this.ssoPassword);
        return loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(),loginBody.getThirdPartySecret(),"expert");
//        return AjaxResult.success();
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        return loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(),loginBody.getThirdPartySecret(),"zcxh");
//        return AjaxResult.success();
    }
//    @PostMapping("/login")
//    public AjaxResult login(@RequestBody LoginBody loginBody)
//    {
//        AjaxResult ajax = AjaxResult.success();
//        // 生成令牌
//        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
//                loginBody.getUuid());
//        ajax.put(Constants.TOKEN, token);
//
//        return ajax;
//    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
//        Set<String> permissions = new HashSet<>();
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        LoginUser user = SecurityUtils.getLoginUser();
        List<SysMenu> menus = new ArrayList<>();
        if(user!=null && user.getUser()!=null) {
            List<SysRole> roles = user.getUser().getRoles();
            boolean isAdmin = false;
            log.info("---------------getRouters--------------------");
            log.info(roles);
            if(roles==null){
                roles = sysRoleService.selectRolesByUserId(user.getUserId());
            }
            for (SysRole role : roles) {
                log.info(role);
            }
            for (SysRole role : roles) {
                if (role.getRoleKey().equals("admin") || role.getRoleKey().equals("xiaoheAI")) {
                    isAdmin = true;
                    break;
                }else if(role.getRoleKey().equals("expert")){
                    return AjaxResult.success(new ArrayList<>());
                }
            }
            if (isAdmin) {
                menus = menuService.selectMenuTreeByUserId(userId);
            } else if (user.getUser().getEnt() != null) {
                BaseEntInfo ent = user.getUser().getEnt();
                if (ent.getEntStatus() == 0) {
                    menus = menuService.selectMenuTreeByUserId(userId);
                }
            }

        }
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
