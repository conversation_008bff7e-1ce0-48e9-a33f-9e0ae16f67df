package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import com.ruoyi.busi.service.IBusiVenueOccupyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 场地占用Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "场地占用管理")
@RestController
@RequestMapping("/venue/occupy")
public class BusiVenueOccupyController extends BaseController {
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;

/**
 * 查询场地占用列表
 */
//@PreAuthorize("@ss.hasPermi('venue:occupy:list')")
@ApiOperation(value = "查询场地占用列表")
@GetMapping("/list")
    public TableDataInfo list(BusiVenueOccupy busiVenueOccupy) {
        startPage();
        List<BusiVenueOccupy> list = busiVenueOccupyService.selectList(busiVenueOccupy);
        return getDataTable(list);
    }

    /**
     * 导出场地占用列表
     */
    @PreAuthorize("@ss.hasPermi('venue:occupy:export')")
    @Log(title = "场地占用", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出场地占用列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiVenueOccupy busiVenueOccupy) {
        List<BusiVenueOccupy> list = busiVenueOccupyService.selectList(busiVenueOccupy);
        ExcelUtil<BusiVenueOccupy> util = new ExcelUtil<BusiVenueOccupy>(BusiVenueOccupy. class);
        util.exportExcel(response, list, "场地占用数据");
    }

    /**
     * 获取场地占用详细信息
     */
    @PreAuthorize("@ss.hasPermi('venue:occupy:query')")
    @ApiOperation(value = "获取场地占用详细信息")
    @ApiImplicitParam(name = "occupyId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{occupyId}")
    public AjaxResult getInfo(@PathVariable("occupyId")Long occupyId) {
        return success(busiVenueOccupyService.getById(occupyId));
    }

    /**
     * 新增场地占用
     */
    @PreAuthorize("@ss.hasPermi('venue:occupy:add')")
    @Log(title = "场地占用", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增场地占用")
    @PostMapping
    public AjaxResult add(@RequestBody BusiVenueOccupy busiVenueOccupy) {
        busiVenueOccupy.setCreateBy(getUsername());
        busiVenueOccupy.setUpdateBy(getUsername());
        return toAjax(busiVenueOccupyService.save(busiVenueOccupy));
    }

    /**
     * 修改场地占用
     */
    @PreAuthorize("@ss.hasPermi('venue:occupy:edit')")
    @Log(title = "场地占用", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改场地占用")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiVenueOccupy busiVenueOccupy) {
        busiVenueOccupy.setUpdateBy(getUsername());
        return toAjax(busiVenueOccupyService.updateById(busiVenueOccupy));
    }

    /**
     * 删除场地占用
     */
    @PreAuthorize("@ss.hasPermi('venue:occupy:remove')")
    @Log(title = "场地占用", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除场地占用")
    @DeleteMapping("/{occupyIds}")
    public AjaxResult remove(@PathVariable Long[] occupyIds) {
        return toAjax(busiVenueOccupyService.removeByIds(Arrays.asList(occupyIds)));
    }
}
