package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiExpertLeaderVoteRecord;
import com.ruoyi.busi.service.IBusiExpertLeaderVoteRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 专家组长投票记录Controller
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Api(tags = "专家组长投票记录管理")
@RestController
@RequestMapping("/expert/leaderVoteRecord")
public class BusiExpertLeaderVoteRecordController extends BaseController {
    @Autowired
    private IBusiExpertLeaderVoteRecordService busiExpertLeaderVoteRecordService;

/**
 * 查询专家组长投票记录列表
 */
@PreAuthorize("@ss.hasPermi('expert:leaderVoteRecord:list')")
@ApiOperation(value = "查询专家组长投票记录列表")
@GetMapping("/list")
    public TableDataInfo list(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        startPage();
        List<BusiExpertLeaderVoteRecord> list = busiExpertLeaderVoteRecordService.selectList(busiExpertLeaderVoteRecord);
        return getDataTable(list);
    }
    @ApiOperation(value = "查询专家组长")
    @GetMapping("/getProjectLeaderVoteRecord")
    public AjaxResult getProjectLeaderVoteRecord(BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        return  busiExpertLeaderVoteRecordService.getProjectLeaderVoteRecord(busiExpertLeaderVoteRecord);
    }
    /**
     * 导出专家组长投票记录列表
     */
    @PreAuthorize("@ss.hasPermi('expert:leaderVoteRecord:export')")
    @Log(title = "专家组长投票记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家组长投票记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        List<BusiExpertLeaderVoteRecord> list = busiExpertLeaderVoteRecordService.selectList(busiExpertLeaderVoteRecord);
        ExcelUtil<BusiExpertLeaderVoteRecord> util = new ExcelUtil<BusiExpertLeaderVoteRecord>(BusiExpertLeaderVoteRecord. class);
        util.exportExcel(response, list, "专家组长投票记录数据");
    }

    /**
     * 获取专家组长投票记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('expert:leaderVoteRecord:query')")
    @ApiOperation(value = "获取专家组长投票记录详细信息")
    @ApiImplicitParam(name = "voteRecordId", value = "投票记录id（主键）", required = true, dataType = "Long")
    @GetMapping(value = "/{voteRecordId}")
    public AjaxResult getInfo(@PathVariable("voteRecordId")Long voteRecordId) {
        return success(busiExpertLeaderVoteRecordService.getById(voteRecordId));
    }

    /**
     * 新增专家组长投票记录
     */
//    @PreAuthorize("@ss.hasPermi('expert:leaderVoteRecord:add')")
    @Log(title = "专家组长投票记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家组长投票记录")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        return busiExpertLeaderVoteRecordService.saveBusiExpertLeaderVoteRecord(busiExpertLeaderVoteRecord);
    }

    /**
     * 修改专家组长投票记录
     */
    @PreAuthorize("@ss.hasPermi('expert:leaderVoteRecord:edit')")
    @Log(title = "专家组长投票记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家组长投票记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExpertLeaderVoteRecord busiExpertLeaderVoteRecord) {
        return toAjax(busiExpertLeaderVoteRecordService.updateById(busiExpertLeaderVoteRecord));
    }

    /**
     * 删除专家组长投票记录
     */
    @PreAuthorize("@ss.hasPermi('expert:leaderVoteRecord:remove')")
    @Log(title = "专家组长投票记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家组长投票记录")
    @DeleteMapping("/{voteRecordIds}")
    public AjaxResult remove(@PathVariable Long[] voteRecordIds) {
        return toAjax(busiExpertLeaderVoteRecordService.removeByIds(Arrays.asList(voteRecordIds)));
    }
}
