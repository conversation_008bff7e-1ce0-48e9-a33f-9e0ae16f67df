package com.ruoyi.busi.controller;

import cn.hutool.core.io.resource.ResourceUtil;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.font.FontProvider;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class FtlToPdfConverter {

    private Configuration freemarkerCfg;

    public FtlToPdfConverter() throws IOException {
        // 初始化FreeMarker配置
        freemarkerCfg = new Configuration(Configuration.VERSION_2_3_31);
        // 设置模板目录
        freemarkerCfg.setClassForTemplateLoading(this.getClass(), "/templates");
        // 设置默认编码
        freemarkerCfg.setDefaultEncoding("UTF-8");
    }

    public void convertFtlToPdf(String ftlTemplateName, Map<String, Object> dataModel, String outputPdfPath) throws IOException, TemplateException {
        // 加载模板
        Template template = freemarkerCfg.getTemplate(ftlTemplateName);

        // 使用 StringWriter 作为输出
        StringWriter stringWriter = new StringWriter();
        // 处理模板，将数据模型写入到 StringWriter
        template.process(dataModel, stringWriter);

        // 获取处理后的HTML内容
        String htmlContent = stringWriter.toString();
        String simfang = ResourceUtil.getResource("")+"templates/simfang.ttf";
        PdfFont simfangFont = PdfFontFactory.createFont(simfang, PdfEncodings.IDENTITY_H, false);

        ConverterProperties properties = new ConverterProperties();
        FontProvider fontProvider = new FontProvider();

        fontProvider.addFont(simfangFont.getFontProgram(), PdfEncodings.IDENTITY_H);
        properties.setFontProvider(fontProvider);

        // 将HTML转换为PDF
        try (OutputStream outputStream = new FileOutputStream(outputPdfPath)) {
            HtmlConverter.convertToPdf(htmlContent, outputStream,properties);
        }
    }

//    // 测试转换方法
//    public static void main(String[] args) {
//        try {
//            FtlToPdfConverter converter = new FtlToPdfConverter();
//            Map<String, Object> dataModel = new HashMap<>(); // 创建数据模型
//            converter.convertFtlToPdf("example.ftl", dataModel, "output.pdf");
//            System.out.println("PDF文件生成成功！");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}

