package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 企业员工信息对象 base_ent_staff
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("企业员工信息对象")
@TableName(resultMap = "com.ruoyi.base.mapper.BaseEntStaffMapper.BaseEntStaffResult")
public class BaseEntStaff extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 公告id
     */
    @ApiModelProperty("公告id")
        @TableId(type = IdType.ASSIGN_ID)
    private Long staffId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
            @Excel(name = "企业id")
    private Long entId;
    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
            @Excel(name = "员工名称")
    private String staffName;
    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
            @Excel(name = "员工编号")
    private String staffCode;
    /**
     * 性别
     */
    @ApiModelProperty("性别")
            @Excel(name = "性别")
    private Integer staffSex;
    /**
     * 电话
     */
    @ApiModelProperty("电话")
            @Excel(name = "电话")
    private String staffPhone;
    /**
     * 学历
     */
    @ApiModelProperty("学历")
            @Excel(name = "学历")
    private String staffDegree;
    /**
     * 职称
     */
    @ApiModelProperty("职称")
            @Excel(name = "职称")
    private String staffTitles;
    /**
     * 专业
     */
    @ApiModelProperty("专业")
            @Excel(name = "专业")
    private String staffSpeciality;
    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
            @Excel(name = "身份证号")
    private String staffCertificate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

        }
