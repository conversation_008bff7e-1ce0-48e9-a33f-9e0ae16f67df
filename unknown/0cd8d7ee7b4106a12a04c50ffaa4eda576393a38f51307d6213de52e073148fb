package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiExtractExpertEvade;
import com.ruoyi.busi.service.IBusiExtractExpertEvadeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 专家抽取回避Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "专家抽取回避管理")
@RestController
@RequestMapping("/expert/evade")
public class BusiExtractExpertEvadeController extends BaseController {
    @Autowired
    private IBusiExtractExpertEvadeService busiExtractExpertEvadeService;

/**
 * 查询专家抽取回避列表
 */
@PreAuthorize("@ss.hasPermi('expert:evade:list')")
@ApiOperation(value = "查询专家抽取回避列表")
@GetMapping("/list")
    public TableDataInfo list(BusiExtractExpertEvade busiExtractExpertEvade) {
        startPage();
        List<BusiExtractExpertEvade> list = busiExtractExpertEvadeService.selectList(busiExtractExpertEvade);
        return getDataTable(list);
    }

    /**
     * 导出专家抽取回避列表
     */
    @PreAuthorize("@ss.hasPermi('expert:evade:export')")
    @Log(title = "专家抽取回避", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家抽取回避列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExtractExpertEvade busiExtractExpertEvade) {
        List<BusiExtractExpertEvade> list = busiExtractExpertEvadeService.selectList(busiExtractExpertEvade);
        ExcelUtil<BusiExtractExpertEvade> util = new ExcelUtil<BusiExtractExpertEvade>(BusiExtractExpertEvade. class);
        util.exportExcel(response, list, "专家抽取回避数据");
    }

    /**
     * 获取专家抽取回避详细信息
     */
    @PreAuthorize("@ss.hasPermi('expert:evade:query')")
    @ApiOperation(value = "获取专家抽取回避详细信息")
    @ApiImplicitParam(name = "evadeId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{evadeId}")
    public AjaxResult getInfo(@PathVariable("evadeId")Long evadeId) {
        return success(busiExtractExpertEvadeService.getById(evadeId));
    }

    /**
     * 新增专家抽取回避
     */
    @PreAuthorize("@ss.hasPermi('expert:evade:add')")
    @Log(title = "专家抽取回避", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家抽取回避")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExtractExpertEvade busiExtractExpertEvade) {
        busiExtractExpertEvade.setCreateBy(getUsername());
        busiExtractExpertEvade.setUpdateBy(getUsername());
        return toAjax(busiExtractExpertEvadeService.save(busiExtractExpertEvade));
    }

    /**
     * 修改专家抽取回避
     */
    @PreAuthorize("@ss.hasPermi('expert:evade:edit')")
    @Log(title = "专家抽取回避", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家抽取回避")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExtractExpertEvade busiExtractExpertEvade) {
        busiExtractExpertEvade.setUpdateBy(getUsername());
        return toAjax(busiExtractExpertEvadeService.updateById(busiExtractExpertEvade));
    }

    /**
     * 删除专家抽取回避
     */
    @PreAuthorize("@ss.hasPermi('expert:evade:remove')")
    @Log(title = "专家抽取回避", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家抽取回避")
    @DeleteMapping("/{evadeIds}")
    public AjaxResult remove(@PathVariable Long[] evadeIds) {
        return toAjax(busiExtractExpertEvadeService.removeByIds(Arrays.asList(evadeIds)));
    }
}
