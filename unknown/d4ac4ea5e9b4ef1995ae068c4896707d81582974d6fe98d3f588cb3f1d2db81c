package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 中标结果公告信息对象 busi_winning_bidder_notice
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("中标结果公告信息对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiWinningBidderNoticeMapper.BusiWinningBidderNoticeResult")
public class BusiWinningBidderNotice extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公告id
     */
    @ApiModelProperty("公告id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long noticeId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 公告代码
     */
    @ApiModelProperty("公告代码")
    @Excel(name = "公告代码")
    private String noticeCode;
    /**
     * 公告名称
     */
    @ApiModelProperty("公告名称")
    @Excel(name = "公告名称")
    private String noticeName;
    /**
     * 公告内容
     */
    @ApiModelProperty("公告内容")
    @Excel(name = "公告内容")
    private String noticeContent;
    /**
     * 公告类型
     */
    @ApiModelProperty("公告类型")
    @Excel(name = "公告类型")
    private Integer noticeType;
    /**
     * 公告开始时间
     */
    @ApiModelProperty("公告开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "公告开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date noticeStartTime;
    /**
     * 中标人id
     */
    @ApiModelProperty("中标人id")
    @Excel(name = "中标人id")
    private Long bidderId;
    /**
     * 中标人代码
     */
    @ApiModelProperty("中标人代码")
    @Excel(name = "中标人代码")
    private String bidderCode;

    /**
     * 中标人名称
     */
    @ApiModelProperty("中标人名称")
    @Excel(name = "中标人名称")
    private String bidderName;
    /**
     * 中标金额
     */
    @ApiModelProperty("中标金额")
    @Excel(name = "中标金额")
    private BigDecimal bidAmount;
    /**
     * 排名
     */
    @ApiModelProperty("排名")
    @Excel(name = "排名")
    private Long ranking;
    /**
     * 得分
     */
    @ApiModelProperty("得分")
    @Excel(name = "得分")
    private Double score;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 项目信息
     */
    @TableField(exist = false)
    private BusiTenderProject project;
    @TableField(exist = false)
    private Integer abortiveType;

    @TableField(exist = false)
    private String scoringMethodItemId;

    /**
     * 附件集合
     */
    @TableField(exist = false)
    private List<BusiAttachment> attachments;

    public Long getId() {
        return this.noticeId;
    }
}
