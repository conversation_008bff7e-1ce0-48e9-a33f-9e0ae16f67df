package com.ruoyi.busi.controller;

import com.ruoyi.busi.domain.BusiBidOpening;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.service.IBusiBidOpeningService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 开标记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "开标记录管理")
@RestController
@RequestMapping("/bid/opening")
public class BusiBidOpeningController extends BaseController {
    @Autowired
    private IBusiBidOpeningService busiBidOpeningService;

/**
 * 查询开标记录列表
 */
@PreAuthorize("@ss.hasPermi('bid:opening:list')")
@ApiOperation(value = "查询开标记录列表")
@GetMapping("/list")
    public TableDataInfo list(BusiBidOpening busiBidOpening) {
        startPage();
        List<BusiBidOpening> list = busiBidOpeningService.selectList(busiBidOpening);
        return getDataTable(list);
    }

    /**
     * 导出开标记录列表
     */
    @PreAuthorize("@ss.hasPermi('bid:opening:export')")
    @Log(title = "开标记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出开标记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiBidOpening busiBidOpening) {
        List<BusiBidOpening> list = busiBidOpeningService.selectList(busiBidOpening);
        ExcelUtil<BusiBidOpening> util = new ExcelUtil<BusiBidOpening>(BusiBidOpening. class);
        util.exportExcel(response, list, "开标记录数据");
    }

    /**
     * 获取开标记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bid:opening:query')")
    @ApiOperation(value = "获取开标记录详细信息")
    @ApiImplicitParam(name = "bidOpeningId", value = "投标记录id", required = true, dataType = "Long")
    @GetMapping(value = "/{bidOpeningId}")
    public AjaxResult getInfo(@PathVariable("bidOpeningId")Long bidOpeningId) {
        return success(busiBidOpeningService.selectWithAttachment(bidOpeningId));
    }

    /**
     * 新增开标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:opening:add')")
    @Log(title = "开标记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增开标记录")
    @PostMapping
    public AjaxResult add(@RequestBody BusiBidOpening busiBidOpening) {
        busiBidOpening.setCreateBy(getUsername());
        busiBidOpening.setUpdateBy(getUsername());
        return toAjax(busiBidOpeningService.save(busiBidOpening));
    }

    /**
     * 新增开标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:opening:add')")
    @Log(title = "开标记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增开标记录")
    @PostMapping("/saveWithRecord")
    public Boolean saveWithRecord(@RequestBody BusiBidOpening busiBidOpening) {
        return busiBidOpeningService.saveWithRecord(busiBidOpening);
    }

    /**
     * 修改开标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:opening:edit')")
    @Log(title = "开标记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改开标记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiBidOpening busiBidOpening) {
        busiBidOpening.setUpdateBy(getUsername());
        return toAjax(busiBidOpeningService.updateById(busiBidOpening));
    }

    /**
     * 删除开标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:opening:remove')")
    @Log(title = "开标记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除开标记录")
    @DeleteMapping("/{bidOpeningIds}")
    public AjaxResult remove(@PathVariable Long[] bidOpeningIds) {
        return toAjax(busiBidOpeningService.removeByIds(Arrays.asList(bidOpeningIds)));
    }

    @ApiOperation(value = "查询符合条件的采购项目")
    @GetMapping("/openingProject")
    public AjaxResult openingProject() {
        List<BusiTenderNotice> list = busiBidOpeningService.openingProject();
        return AjaxResult.success(list);
    }

    @GetMapping("/getSystemTime")
    public AjaxResult getSystemTime() {
        return AjaxResult.success(new Date().getTime());
    }
}
