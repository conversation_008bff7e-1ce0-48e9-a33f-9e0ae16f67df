package com.ruoyi.busi.enums;

import java.util.Arrays;

public enum ProjectCodeEnum {
    JZXCS("CS", "竞争性磋商"),
    JZXTP("TP", "竞争性谈判"),
    XJ("XJ", "询价"),
    DYLY("DY", "单一来源");

    private String code;
    private String name;

    ProjectCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ProjectCodeEnum getCodeByName(String name){
        return Arrays.stream(ProjectCodeEnum.values()).filter(rel -> rel.name.equals(name)).findFirst().get();
    }
}
