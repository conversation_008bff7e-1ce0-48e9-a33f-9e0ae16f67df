package com.ruoyi.busi.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购意向对象 busi_tender_intention
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("采购意向对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiTenderIntentionMapper.BusiTenderIntentionResult")
public class BusiTenderIntention extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 意向id
     */
    @ApiModelProperty("意向id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long intentionId;
    /**
     * 意向代码
     */
    @ApiModelProperty("意向代码")
    @Excel(name = "意向代码")
    private String intentionCode;
    /**
     * 意向名称
     */
    @ApiModelProperty("意向名称")
    @Excel(name = "意向名称")
    private String intentionName;
    /**
     * 采购人id
     */
    @ApiModelProperty("采购人id")
    @Excel(name = "采购人id")
    private Long tendererId;

    /**
     * 采购人Name
     */
    @TableField(exist = false)
    private String tendererName;

    /**
     * 意向内容
     */
    @ApiModelProperty("意向内容")
    @Excel(name = "意向内容")
    private String intentionContent;
    /**
     * 预算金额
     */
    @ApiModelProperty("预算金额")
    @Excel(name = "预算金额")
    private BigDecimal budgetAmount;
    /**
     * 项目工期 (天)
     */
    @ApiModelProperty("项目工期 (天)")
    @Excel(name = "项目工期 (天)")
    private Long projectDuration;
    /**
     * 采购方式
     */
    @ApiModelProperty("采购方式")
    @Excel(name = "采购方式")
    private String tenderMode;
    /**
     * 意向开始时间
     */
    @ApiModelProperty("意向开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "意向开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date intentionStartTime;
    /**
     * 意向结束时间
     */
    @ApiModelProperty("意向结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "意向结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date intentionEndTime;

    private String baseContent;
    /**
     * 删除标记 (0正常 1删除)
     */
    @ApiModelProperty("删除标记 (0正常 1删除)")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<BusiAttachment> attachments;


}
