package com.ruoyi.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.base.vo.DayType;
import com.ruoyi.base.vo.DayTypeDayTime;
import com.ruoyi.base.vo.Days;
import com.ruoyi.base.vo.SiteOccupationVO;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import com.ruoyi.busi.service.IBusiVenueOccupyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.base.mapper.BaseVenueInfoMapper;
import com.ruoyi.base.domain.BaseVenueInfo;
import com.ruoyi.base.service.IBaseVenueInfoService;

/**
 * 场地信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BaseVenueInfoServiceImpl extends ServiceImpl<BaseVenueInfoMapper, BaseVenueInfo> implements IBaseVenueInfoService {
    @Autowired
    private IBaseVenueInfoService baseVenueInfoService;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;

    /**
     * 查询场地信息列表
     *
     * @param baseVenueInfo 场地信息
     * @return 场地信息
     */
    @Override
    public List<BaseVenueInfo> selectList(BaseVenueInfo baseVenueInfo) {
        QueryWrapper<BaseVenueInfo> baseVenueInfoQueryWrapper = new QueryWrapper<>();
                        baseVenueInfoQueryWrapper.like(ObjectUtil.isNotEmpty(baseVenueInfo.getVenueName()),"venue_name",baseVenueInfo.getVenueName());
                        baseVenueInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseVenueInfo.getVenueType()),"venue_type",baseVenueInfo.getVenueType());
                        baseVenueInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseVenueInfo.getVenueSpec()),"venue_spec",baseVenueInfo.getVenueSpec());
                        baseVenueInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseVenueInfo.getRemark()),"remark",baseVenueInfo.getRemark());
                        baseVenueInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseVenueInfo.getDelFlag()),"del_flag",baseVenueInfo.getDelFlag());
                    String beginCreateTime = baseVenueInfo.getParams().get("beginCreateTime")!=null?baseVenueInfo.getParams().get("beginCreateTime")+"":"";
                    String endCreateTime = baseVenueInfo.getParams().get("endCreateTime")+""!=null?baseVenueInfo.getParams().get("endCreateTime")+"":"";
                        baseVenueInfoQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime , endCreateTime);
                        baseVenueInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseVenueInfo.getCreateBy()),"create_by",baseVenueInfo.getCreateBy());
                    String beginUpdateTime = baseVenueInfo.getParams().get("beginUpdateTime")!=null?baseVenueInfo.getParams().get("beginUpdateTime")+"":"";
                    String endUpdateTime = baseVenueInfo.getParams().get("endUpdateTime")+""!=null?baseVenueInfo.getParams().get("endUpdateTime")+"":"";
                        baseVenueInfoQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime , endUpdateTime);
                        baseVenueInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseVenueInfo.getUpdateBy()),"update_by",baseVenueInfo.getUpdateBy());
        return list(baseVenueInfoQueryWrapper);
    }

    @Override
    public List<SiteOccupationVO> getSiteOccupationList(String venueType) {
        //获取全部场地
        List<SiteOccupationVO> siteOccupationVOS=new ArrayList<>();
        BaseVenueInfo baseVenueInfo=new BaseVenueInfo();
        baseVenueInfo.setVenueType(venueType);
        baseVenueInfo.setDelFlag(0);
        List<BaseVenueInfo> list = baseVenueInfoService.list(new QueryWrapper<BaseVenueInfo>().eq("venue_type",venueType));

        for (BaseVenueInfo venueInfo : list) {
            SiteOccupationVO siteOccupationVO=new SiteOccupationVO();
            List<BusiVenueOccupy> busiVenueOccupies=new ArrayList<>();
            QueryWrapper<BusiVenueOccupy> queryWrapper=new QueryWrapper<BusiVenueOccupy>();
            queryWrapper.eq("venue_id",venueInfo.getVenueId());
            queryWrapper.eq("venue_type",venueType);
            siteOccupationVO.setVenueId(venueInfo.getVenueId());
            siteOccupationVO.setVenueName(venueInfo.getVenueName());
            siteOccupationVO.setVenueType(venueType);
            //获取最近15天
            List<String> fifteenDays=get15Days();
            queryWrapper.between("occupy_start_time", fifteenDays.get(0), fifteenDays.get(fifteenDays.size()-1));
            busiVenueOccupies = busiVenueOccupyService.list(queryWrapper);
            // 创建SimpleDateFormat对象，用于格式化日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 遍历days列表
            List<Days> daysvo=new ArrayList<>();
            for (String day : fifteenDays) {
                Days days=new Days();
                days.setDate(day);
                List<DayType> dayTypes=new ArrayList<>();
                List<DayTypeDayTime> dayTypeDayTimes=new ArrayList<>();
                dayTypes.add(new DayType(0,0));
                dayTypes.add(new DayType(1,0));
                dayTypes.add(new DayType(2,0));
                days.setDayTypeList(dayTypes);
                // 获取日期等于day 的占用记录
                List<BusiVenueOccupy> filteredList = busiVenueOccupies.stream()
                        .filter(busiVenueOccupy -> {
                            // 获取busiVenueOccupy的createTime并格式化为字符串
                            String createTimeStr = dateFormat.format(busiVenueOccupy.getOccupyStartTime());
                            // 比较格式化后的createTime与str是否相等
                            return day.equals(createTimeStr);
                        })
                        .collect(Collectors.toList());
                // 检查filteredList是否不为null且不为空
                if (filteredList != null && filteredList.size()>0) {
                    if (venueType.equals("1")){
                        SimpleDateFormat dateFormat1 = new SimpleDateFormat("HH:mm:ss");
                        for (BusiVenueOccupy busiVenueOccupy : filteredList) {
                            DayTypeDayTime dayTypeDayTime=new DayTypeDayTime();
                            dayTypeDayTime.setStartTime(dateFormat1.format(busiVenueOccupy.getOccupyStartTime()));
                            dayTypeDayTime.setEndTime(dateFormat1.format(busiVenueOccupy.getOccupyEndTime()));
                            dayTypeDayTimes.add(dayTypeDayTime);
                        }
                        days.setSpaceOccupancyList(dayTypeDayTimes);
                    }else if (venueType.equals("2")){
                        long zeroCount = filteredList.stream().filter(e -> e.getBidEvaluationPeriod().equals(0)).count();
                        long oneCount = filteredList.stream().filter(e -> e.getBidEvaluationPeriod().equals(1)).count();
                        long twoCount = filteredList.stream().filter(e -> e.getBidEvaluationPeriod().equals(2)).count();
                        dayTypes.stream().filter(e->e.getType().equals(0)).findFirst().orElse(null).setStatus(zeroCount > 0 ? 1 : 0);
                        dayTypes.stream().filter(e->e.getType().equals(1)).findFirst().orElse(null).setStatus(oneCount > 0 ? 1 : 0);
                        dayTypes.stream().filter(e->e.getType().equals(2)).findFirst().orElse(null).setStatus(twoCount > 0 ? 1 : 0);
                    }
                }
                days.setSpaceOccupancyList(dayTypeDayTimes);
                daysvo.add(days);
                siteOccupationVO.setDayList(daysvo);
            }

            siteOccupationVOS.add(siteOccupationVO);
        }
        return  siteOccupationVOS;
    }

    List<String>  get15Days(){
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 初始化日期列表
        List<String> dates = new ArrayList<>();

        // 循环生成未来15天的日期
        for (int i = 0; i <= 14; i++) {
            // 计算日期
            LocalDate date = today.plusDays(i);
            // 将日期格式化为字符串并添加到列表中
            String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dates.add(formattedDate);
        }
        return dates;
    }

}
