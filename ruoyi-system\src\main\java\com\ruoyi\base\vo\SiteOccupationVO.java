package com.ruoyi.base.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SiteOccupationVO implements Serializable {

    /**
     * 场地id
     */
    @ApiModelProperty("场地id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long venueId;
    /**
     * 场地名称
     */
    @ApiModelProperty("场地名称")
    @Excel(name = "场地名称")
    private String venueName;

    @ApiModelProperty("场地类型 (1开标 2评标)")
    @Excel(name = "场地类型 (1开标 2评标)")
    private String venueType;

    List<Days> dayList;

//    List<Days> spaceOccupancyList;

}
