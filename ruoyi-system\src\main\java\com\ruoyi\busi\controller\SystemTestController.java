package com.ruoyi.busi.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.itextpdf.barcodes.BarcodeQRCode;
import com.itextpdf.barcodes.qrcode.EncodeHintType;
import com.itextpdf.barcodes.qrcode.ErrorCorrectionLevel;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.io.source.IRandomAccessSource;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.PdfCanvasProcessor;
import com.itextpdf.kernel.pdf.canvas.parser.PdfTextExtractor;
import com.itextpdf.kernel.pdf.canvas.parser.listener.IPdfTextLocation;
import com.itextpdf.kernel.pdf.canvas.parser.listener.RegexBasedLocationExtractionStrategy;
import com.itextpdf.kernel.pdf.navigation.PdfDestination;
import com.itextpdf.kernel.pdf.navigation.PdfExplicitDestination;
import com.itextpdf.kernel.pdf.navigation.PdfExplicitRemoteGoToDestination;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.font.FontProvider;
import com.itextpdf.layout.property.AreaBreakType;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.layout.property.VerticalAlignment;
import com.itextpdf.layout.renderer.DrawContext;
import com.itextpdf.layout.renderer.IRenderer;
import com.itextpdf.layout.renderer.TextRenderer;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.busi.service.IBusiVenueOccupyService;
import com.ruoyi.system.service.ISysDictDataService;
import freemarker.template.Template;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.fop.apps.FOUserAgent;
import org.apache.fop.apps.Fop;
import org.apache.fop.apps.FopFactory;
import org.apache.xmlgraphics.util.MimeConstants;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.sax.SAXResult;
import javax.xml.transform.stream.StreamSource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.*;


@RestController
@RequestMapping("/systemTest")
public class SystemTestController {

    @Resource
    private FreeMarkerConfigurer configurer;
    @Autowired
    private IBusiTenderNoticeService tenderNoticeService;
    @Autowired
    private IBusiTenderProjectService tenderProjectService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;

    @GetMapping("/createNoticeContent")
    public String createNoticeContent(Long noticeId){
        try {
            BusiTenderNotice notice = tenderNoticeService.getById(noticeId);
            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
            project.setTenderModeName(dictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode()));
            project.setProjectTypeName(dictDataService.selectDictLabel("busi_project_type", project.getProjectType()));
            if(StringUtils.isNoneBlank(project.getTenderFundSource())) {
                project.setTenderFundSourceName(dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
            }
            BusiVenueOccupy query = new BusiVenueOccupy();
            query.setNoticeId(noticeId);
            List<BusiVenueOccupy> occupyList = busiVenueOccupyService.selectList(query);
            for (BusiVenueOccupy occupy : occupyList) {
                if (occupy.getVenueType() == 1) {
                    notice.setBidOpening(occupy);
                }else{
                    notice.setBidEvaluation(occupy);
                }
            }

            BusiTenderNotice oldTenderNotice = null;
            if (notice.getNoticeType() == 2) {
                BusiTenderNotice oldNoticeQuery = new BusiTenderNotice();
                oldNoticeQuery.setProjectId(notice.getProjectId());
                oldNoticeQuery.setChangeNum(notice.getChangeNum()-1);
                oldTenderNotice = tenderNoticeService.getOneIgnoreDeleted(oldNoticeQuery);
                BusiVenueOccupy query2 = new BusiVenueOccupy();
                query2.setNoticeId(oldTenderNotice.getNoticeId());
                List<BusiVenueOccupy> occupyList2 = busiVenueOccupyService.getListIgnoreDeleted(query2);
                for (BusiVenueOccupy occupy : occupyList2) {
                    if (occupy.getVenueType() == 1) {
                        oldTenderNotice.setBidOpening(occupy);
                    }else{
                        oldTenderNotice.setBidEvaluation(occupy);
                    }
                }
            }
            String content = tenderNoticeService.getMainContent(project, notice, oldTenderNotice);
            System.out.println("--------------------------------------------");
            System.out.println(content);
            return content;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @ApiOperation(value = "供应商统计招投标信息")
    @GetMapping("/pdf")
    public void pdf(HttpServletRequest request, HttpServletResponse response, Long tenderNoticeId){
        try {

            byte[] bs = getbyte();
            genCode(response, bs);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ApiOperation(value = "供应商统计招投标信息")
    @GetMapping("/toWord")
    public String toWord(HttpServletRequest request, HttpServletResponse response){
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("test1", "20240702测试论证项目信息表导出");
            map.put("test2", "2024年7月2日");
            map.put("test3", "ja");
            map.put("test4", "你猜猜");

            Template template = configurer.getConfiguration().getTemplate("test3.ftl");
            File f = new File("d:/test.docx");
            if (f.exists()) {
                f.delete();
            }
//                 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
                Writer w = new OutputStreamWriter(new FileOutputStream(f), "utf-8");
                template.process(map, w);
                w.close();

            // 读取Word文档
            IRandomAccessSource s;
//            RandomAccessFileOrArray ra = new RandomAccessFileOrArray(IRandomAccessSource"output.docx");
            PdfReader reader = new PdfReader("d:/test2.doc");

            // 创建PDF文档
            PdfDocument pdf = new PdfDocument(reader, new PdfWriter(new FileOutputStream("d:/test.pdf")));

            // 添加内容到PDF文档
            Document document = new Document(pdf);
            for (int page = 1; page <= pdf.getNumberOfPages(); page++) {
                String text = PdfTextExtractor.getTextFromPage(pdf.getPage(page));
                document.add(new Paragraph(text));
            }

            // 关闭文档
            document.close();
            pdf.close();
//            ra.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "haha";
    }

    @GetMapping("/test2")
    public void test2(){
        try {
            String bjt2 = ResourceUtil.getResource("").getPath();
            String bjt = ResourceUtil.getResource("").getPath()+"templates/评审记录.xls";
            System.out.println(bjt.replace("",bjt));
            FileInputStream inputStream = new FileInputStream(bjt);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void  testKey(){
        String pdfFilePath = "d:/111.pdf"; // PDF文件路径
        String keyword = "20150605"; // 要提取的关键字

        try {
            PdfReader reader1 = new PdfReader(pdfFilePath);
            PdfDocument pdfDoc = new PdfDocument(reader1);
            PdfWriter writer = new PdfWriter(pdfFilePath);
            for (int page = 1; page <= pdfDoc.getNumberOfPages(); ++page) {
                String pageContent = PdfTextExtractor.getTextFromPage(pdfDoc.getPage(page));
                if (pageContent.contains(keyword)) {
                    System.out.println("Keyword found on page " + page + ": " + pageContent);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void testGoto() throws Exception {
        try {
            manipulatePdf();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static final String file1 = "d:/222.pdf";
    public static final String file2 = "d:/111.pdf";
    public static final String file3 = "d:/磋商文件.pdf";

    private static List<AbstractMap.SimpleEntry<String, PdfDestination>> list = new ArrayList<>();
    protected static void manipulatePdf() throws Exception {
        File f1 = new File(file1);
        if (f1.exists()) {
            f1.delete();
        }
        PdfDocument pdfDoc = new PdfDocument(new PdfReader(file3), new PdfWriter(file1));
//        PdfDocument pdfDoc = new PdfDocument(new PdfReader(file3));
//        PdfDocument pdfDoc = new PdfDocument(new PdfWriter(file2));
        Document doc = new Document(pdfDoc);
        Rectangle[] columns = {
                new Rectangle(36, 36, 173, 770),
                new Rectangle(213, 36, 173, 770),
                new Rectangle(389, 36, 173, 770)
        };

//        doc.setRenderer(new ColumnDocumentRenderer(doc, columns));
//        PdfOutline root = pdfDoc.getOutlines(false);
//        pdfDoc.getOutlines(true).addOutline("第一章 引言").
//                addAction(PdfAction.createGoTo(PdfExplicitRemoteGoToDestination.createXYZ(1,10,20,0)));

//        for (int i = 0; i <= 20; i++) {
//            int start = (i * 10) + 1;
//            int end = (i + 1) * 10;
//            String title = String.format("Numbers2 from %s to %s", start, end);
//            Text c = new Text(title);
//            TOCTextRenderer renderer = new TOCTextRenderer(c);
//            renderer.setRoot(root);
//            c.setNextRenderer(renderer);
////            doc.add(new Paragraph(c));
////            doc.add(createTable(start, end));
//        }

//        doc.add(new AreaBreak());
        System.out.println(pdfDoc.getNumberOfPages());
        for(int i=1;i<=pdfDoc.getNumberOfPages();i++) {
            RegexBasedLocationExtractionStrategy strategy = new RegexBasedLocationExtractionStrategy("磋商报价得分");
            PdfCanvasProcessor canvasProcessor = new PdfCanvasProcessor(strategy);
            canvasProcessor.processPageContent(pdfDoc.getPage(i));
            Collection<IPdfTextLocation> resultantLocations = strategy.getResultantLocations();
            List<IPdfTextLocation> iPdfTextLocationList = CollectionUtil.newArrayList(resultantLocations);
            for (IPdfTextLocation x : iPdfTextLocationList) {
//                PdfDestination destination = PdfExplicitDestination.createXYZ(pdfDoc.getPage(i), x.getRectangle().getLeft(), x.getRectangle().getTop(), 0);
                System.out.println(i);
                System.out.println(x.getText());
                System.out.println(x.getRectangle());
                System.out.println(x.getRectangle().getX());
                System.out.println(x.getRectangle().getY());
                System.out.println(x.getRectangle().getTop());
                System.out.println(x.getRectangle().getBottom());
                pdfDoc.getOutlines(true).addOutline(x.getText()).
                addAction(PdfAction.createGoTo(PdfExplicitRemoteGoToDestination.createXYZ(i,x.getRectangle().getLeft(),x.getRectangle().getTop(),0)));
            }
        }
//        for (AbstractMap.SimpleEntry<String, PdfDestination> entry : list) {
//            Link c = new Link(entry.getKey(), entry.getValue());
//            doc.add(new Paragraph(c));
//        }

        doc.close();
    }

    private static Table createTable(int start, int end) {
        Table table = new Table(UnitValue.createPercentArray(2)).useAllAvailableWidth();
        for (int i = start; i <= end; i++) {
            table.addCell(new Cell().add(new Paragraph(String.valueOf(i))));
            table.addCell(new Cell().add(new Paragraph("Test")));
        }
        return table;
    }

    private static class TOCTextRenderer extends TextRenderer {
        protected PdfOutline root;

        public TOCTextRenderer(Text modelElement) {
            super(modelElement);
        }

        public void setRoot(PdfOutline root) {
            this.root = root;
        }

        // If a renderer overflows on the next area, iText uses #getNextRenderer() method to create a new renderer for the overflow part.
        // If #getNextRenderer() isn't overridden, the default method will be used and thus the default rather than the custom
        // renderer will be created
        @Override
        public IRenderer getNextRenderer() {
            return new TOCTextRenderer((Text) modelElement);
        }

        @Override
        public void draw(DrawContext drawContext) {
            super.draw(drawContext);
            Rectangle rect = getOccupiedAreaBBox();
            PdfPage page = drawContext.getDocument().getPage(getOccupiedArea().getPageNumber());
            PdfDestination dest = PdfExplicitDestination.createXYZ(page, rect.getLeft(), rect.getTop(), 0);

            list.add(new AbstractMap.SimpleEntry<String, PdfDestination>(((Text) modelElement).getText(), dest));

            PdfOutline curOutline = root.addOutline(((Text) modelElement).getText());
            curOutline.addDestination(dest);
        }
    }


    /**
     * 生成zip文件
     */
    private void genCode(HttpServletResponse response, byte[] data) throws IOException
    {
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"tenderFile.pdf\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }

    private byte[] getbyte() throws Exception {
        return change2PDF2();
    }

    private byte[] change2PDF2() throws Exception {

        Map<String, Object> map = new HashMap<>();
        JSONArray ja = new JSONArray();
        for(int i=0;i<4;i++){
            JSONObject jo = new JSONObject();
            jo.put("zjxm", "专家" + i);
            jo.put("lxdh", "联系电话" + i);
            jo.put("gzdw", "工作单位" + i);
            ja.add(jo);
        }
        map.put("projectName", "20240702测试论证项目信息表导出");
        map.put("lzsj", "2024年7月2日");
        map.put("items", ja);
//        map.put("tenderName", tenderProject.getTendererName());
//        map.put("agentName", tenderProject.getAgencyName());
//        map.put("projectCode", tenderProject.getProjectCode());
//        map.put("projectName", tenderProject.getProjectName());
//        map.put("projectTime", DateUtils.getChineseDate(winningBidderNotice.getCreateTime()));
//        map.put("bidderAmount", String.valueOf(winningBidderNotice.getBidAmount().setScale(2, RoundingMode.HALF_UP)));
//        map.put("bidderAmountBig", NumberUtil.dealMoney(winningBidderNotice.getBidAmount().setScale(2, RoundingMode.HALF_UP)));

        Template template = configurer.getConfiguration().getTemplate("专家登记表.ftl");
        String res = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        try{
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter pdfWriter = new PdfWriter(baos);
            PdfDocument pdfDocument = new PdfDocument(pdfWriter);

            pdfDocument.setDefaultPageSize(PageSize.A4.rotate());
            Document document = new Document(pdfDocument);
            PdfPage pdfPage = pdfDocument.addNewPage();
            PdfCanvas canvas = new PdfCanvas(pdfPage);
            String simfang = ResourceUtil.getResource("")+"templates/simfang.ttf";
            PdfFont simfangFont = PdfFontFactory.createFont(simfang, PdfEncodings.IDENTITY_H, false);

            ConverterProperties properties = new ConverterProperties();
            FontProvider fontProvider = new FontProvider();

            fontProvider.addFont(simfangFont.getFontProgram(), PdfEncodings.IDENTITY_H);
            properties.setFontProvider(fontProvider);

            InputStream inputStream = new ByteArrayInputStream(res.getBytes(StandardCharsets.UTF_8));

            //添加背景图片
            String bjt = ResourceUtil.getResource("")+"templates/11.png";
            Image image = new Image(ImageDataFactory.create(bjt));
            image.setAutoScale(true);
            image.setBackgroundColor(new DeviceRgb(255, 255, 255));

            //添加签章
            String yz = ResourceUtil.getResource("")+"templates/yz.png";
            ImageData d = ImageDataFactory.create(yz);
            d.setWidth(100);
            d.setHeight(100);
            Image yzImage = new Image(d);
            yzImage.setWidth(50);
            yzImage.setHeight(50);
//            yzImage.scaleToFit(50, 50);
            yzImage = yzImage.setBackgroundColor(new DeviceRgb(255, 255, 255));

            // 生成二维码
            Map<EncodeHintType, Object> mHints = new HashMap<>();
            mHints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q);
            mHints.put(EncodeHintType.MIN_VERSION_NR, 2);
            BarcodeQRCode qrCode = new BarcodeQRCode("http://192.168.6.88:8080/systemTest/pdf", mHints);
            Image qrCodeImage = new Image(qrCode.createFormXObject(ColorConstants.BLACK, 2, pdfDocument));
            qrCodeImage.setWidth(100);
            qrCodeImage.setHeight(100);


            canvas.addXObject(image.getXObject(), 0, 0);
            canvas.addXObject(yzImage.getXObject(), 500, 100);
            canvas.addXObject(qrCodeImage.getXObject(), 650, 420);

            //增加水印
            Paragraph watermark = new Paragraph("鹤壁市政府采购协会").setFont(simfangFont).setFontSize(14).setOpacity(0.2f);
            for (int i = 1; i <= 6; i++) {
                for(int j = 0; j<5; j++) {
                    float x = j*200;
                    float y = i*150;
                    document.showTextAligned(
                            watermark, x, y, 1, TextAlignment.LEFT, VerticalAlignment.BOTTOM, (float) (Math.PI/5));
                }
            }

            HtmlConverter.convertToPdf(inputStream, pdfDocument, properties);
            byte[] bts = baos.toByteArray();
            baos.close();
            pdfWriter.close();
            pdfDocument.close();
            document.close();

            return bts;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 给已经输出的pdf文件添加水印
     * @param
     * @throws Exception
     */
//    public static void setPdfWaterMarForFileTest(String filePath, String fileName, String waterText) throws Exception {
//        // 输入PDF文件路径
//        String inputFile = filePath + fileName;
//        // 输出PDF文件路径
//        String outputFile = filePath +"temp_" + fileName;
//        // 创建PdfReader对象读取原始PDF文件
//        PdfReader reader1 = new PdfReader(inputFile);
//        // 创建PdfStamper对象，用于添加水印
//        PdfStamper stamper = new PdfStamper(reader1, new FileOutputStream(outputFile));
//        // 用于添加水印的主要对象
//        PdfContentByte waterMar;
//        // 创建水印字体
//        String simfang = "d:/simfang.ttf";
//        BaseFont base = BaseFont.createFont(simfang, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
//        // 设置水印透明度
//        PdfGState gs = new PdfGState();
//        // 设置填充字体不透明度
//        gs.setFillOpacity(0.12f);
//        gs.setStrokeOpacity(0.3f);
//        // 获取pdf页数
//        int n = reader1.getNumberOfPages();
//        // 添加水印到每一页
//        for (int i = 1; i <= n; i++) {
//            waterMar = stamper.getOverContent(i);
//            // 水印的设置方法，主要用于初始化字体数据和前期准备
//            waterMar.saveState();
//            waterMar.restoreState();
//            waterMar.setGState(gs);
//            waterMar.setFontAndSize(base, 12);
//            // 开始设置水印
//            waterMar.beginText();
//            // 第一个为字体的对齐方式，第二个是水印文本，第三个是水印x坐标，第四个为y坐标，最后一个参数为旋转度数
//            waterMar.showTextAligned(Element.ALIGN_LEFT, waterText, 0, 0, 0);
//            // 设置水印结束
//            waterMar.endText();
//        }
//        // 关闭PdfStamper和PdfReader对象
//        stamper.close();
//        reader1.close();
//    }
    public static void convertXmlToPdf(String xmlFilePath, String xslFilePath, String pdfFilePath) throws Exception {
        // 初始化Fop工厂
        FopFactory fopFactory = FopFactory.newInstance(new File(".").toURI());

        // 设置输出文件
        OutputStream out = new BufferedOutputStream(new FileOutputStream(pdfFilePath));
        try {
            // 创建FOP用户代理
            FOUserAgent foUserAgent = fopFactory.newFOUserAgent();

            // 创建FOP实例
            Fop fop = fopFactory.newFop(MimeConstants.MIME_PDF, foUserAgent, out);

            // 设置Transformer
            TransformerFactory factory = TransformerFactory.newInstance();
            Transformer transformer = factory.newTransformer(new StreamSource(new File(xslFilePath)));

            // 将源XML和XSL样式表转换到FOP
            Source src = new StreamSource(new File(xmlFilePath));
            Result res = new SAXResult(fop.getDefaultHandler());

            // 执行转换
            transformer.transform(src, res);
        } finally {
            out.close();
        }
    }
    public static void convertDocToHtml() throws IOException, ParserConfigurationException {
        String xmlFilePath = "d:/test.xml";
        String htmlFilePath = "d:/test.html";
        String pdfFilePath = "d:/test.pdf";

        // Step 1: Convert XML to HTML
        try (FileInputStream fis = new FileInputStream(xmlFilePath);
             FileOutputStream fos = new FileOutputStream(htmlFilePath)) {
            org.jsoup.nodes.Document doc = Jsoup.parse(fis, "UTF-8", "");
            // Perform necessary manipulations to convert XML to HTML
            // For example, you might want to replace specific tags or add CSS styles
            // doc.select("your-xml-tag").each(e -> e.html("new-html-content"));
            // doc.outputSettings(new Document.OutputSettings().prettyPrint(true));
            String htmlContent = doc.html();

            // Write the String to the HTML file
                fos.write(htmlContent.getBytes());
        } catch (IOException e) {
            e.printStackTrace();
        }

        // Step 2: Convert HTML to PDF
        try (FileInputStream fis = new FileInputStream(htmlFilePath);
             FileOutputStream fos = new FileOutputStream(pdfFilePath)) {
            PdfDocument pdf = new PdfDocument(new PdfWriter(fos));
//            Document layoutDoc = new Document(pdf);
            // Load the HTML content into a layout document
            // layoutDoc.open();
            // layoutDoc.setHtml("<html><body>Your HTML content here</body></html>");
            // layoutDoc.close();
            pdf.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static  void tttest(){
        try {
            // 指定HTML文件和PDF文件的路径
            String htmlPath = "path/to/your/html/file.html";
            String pdfPath = "d:/file.pdf";
            String html = "<html><body><p>First page</p><p>Second page</p></body></html>";

            // 创建PdfWriter实例
            PdfWriter writer = new PdfWriter(new FileOutputStream(pdfPath));
            // 创建PdfDocument实例
            PdfDocument pdf = new PdfDocument(writer);
            // 创建ConverterProperties实例
            ConverterProperties properties = new ConverterProperties();

            // 设置转换属性，比如字符编码
            properties.setCharset(StandardCharsets.UTF_8.toString());

            // 创建Document实例
            Document document = new Document(pdf);

            // 读取HTML文件
            // 将HTML转换为IBlockElement列表
            java.util.List<IElement> htmlElements = HtmlConverter.convertToElements(html, properties);

            // 遍历IBlockElement列表，并添加到PDF文档中
            for (IElement element : htmlElements) {
                document.add((IBlockElement)element);
                // 在这里可以插入分页逻辑，例如在每个特定元素后添加分页
                document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
            }

            // 关闭文档
            document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) throws IOException, ParserConfigurationException {
        String docxFilePath = "d:/test.docx";
        String pdfFilePath = "d:/test.pdf";
        SystemTestController.tttest();
    }
}
