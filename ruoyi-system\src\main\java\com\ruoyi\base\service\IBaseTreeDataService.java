package com.ruoyi.base.service;

import java.util.List;
import com.ruoyi.base.domain.BaseTreeData;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 多级数据Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBaseTreeDataService extends IService<BaseTreeData> {
    /**
     * 查询多级数据列表
     *
     * @param baseTreeData 多级数据
     * @return 多级数据集合
     */
    public List<BaseTreeData> selectList(BaseTreeData baseTreeData);

    /**
     * 查询树形数据
     * @param baseTreeData
     * @return
     */
    List<BaseTreeData> selectListWithTree(BaseTreeData baseTreeData);
}