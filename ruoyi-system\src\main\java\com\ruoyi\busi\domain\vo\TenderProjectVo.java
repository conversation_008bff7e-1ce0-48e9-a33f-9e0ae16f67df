package com.ruoyi.busi.domain.vo;

import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.math.RoundingMode;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class TenderProjectVo extends BusiTenderProject {

    private String tenderModeName;
    private String projectIndustryName;
    private String tenderFundSourceName;

}
