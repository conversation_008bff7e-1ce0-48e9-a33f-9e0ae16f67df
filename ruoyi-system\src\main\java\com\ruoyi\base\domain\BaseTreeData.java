package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 多级数据对象 base_tree_data
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@EqualsAndHashCode
@ApiModel("多级数据对象")
@TableName(resultMap = "com.ruoyi.base.mapper.BaseTreeDataMapper.BaseTreeDataResult")
public class BaseTreeData extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 父id
     */
    @ApiModelProperty("父id")
    @Excel(name = "父id")
    private Long pid;
    /**
     * 第三方id
     */
    @ApiModelProperty("第三方id")
    @Excel(name = "第三方id")
    private String thirdId;
    /**
     * 第三方父子关系
     */
    @ApiModelProperty("第三方父子关系")
    @Excel(name = "第三方父子关系")
    private String thirdPid;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;
    /**
     * 代码
     */
    @ApiModelProperty("代码")
    @Excel(name = "代码")
    private String code;
    /**
     * 类型
     */
    @ApiModelProperty("类型")
    @Excel(name = "类型")
    private Integer type;
    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用 0否1是")
    @Excel(name = "是否启用")
    private Integer isEnabled;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    @Excel(name = "排序")
    private Long sort;
    /**
     * 是否删除0否1是
     */
    @ApiModelProperty("是否删除0否1是")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 子元素
     */
    @ApiModelProperty("子元素")
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<BaseTreeData> childrens = new ArrayList<>();

    @ApiModelProperty("字符串冗余字段1")
    @TableField(exist = false)
    private String strField1;

}
