package com.ruoyi.base.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.base.domain.BaseEntQualification;
import com.ruoyi.base.service.IBaseEntQualificationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 企业资质Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "企业资质管理")
@RestController
@RequestMapping("/ent/qualification")
public class BaseEntQualificationController extends BaseController {
    @Autowired
    private IBaseEntQualificationService baseEntQualificationService;

/**
 * 查询企业资质列表
 */
@PreAuthorize("@ss.hasPermi('ent:qualification:list')")
@ApiOperation(value = "查询企业资质列表")
@GetMapping("/list")
    public TableDataInfo list(BaseEntQualification baseEntQualification) {
        startPage();
        List<BaseEntQualification> list = baseEntQualificationService.selectList(baseEntQualification);
        return getDataTable(list);
    }

    /**
     * 导出企业资质列表
     */
    @PreAuthorize("@ss.hasPermi('ent:qualification:export')")
    @Log(title = "企业资质", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出企业资质列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseEntQualification baseEntQualification) {
        List<BaseEntQualification> list = baseEntQualificationService.selectList(baseEntQualification);
        ExcelUtil<BaseEntQualification> util = new ExcelUtil<BaseEntQualification>(BaseEntQualification. class);
        util.exportExcel(response, list, "企业资质数据");
    }

    /**
     * 获取企业资质详细信息
     */
    @PreAuthorize("@ss.hasPermi('ent:qualification:query')")
    @ApiOperation(value = "获取企业资质详细信息")
    @ApiImplicitParam(name = "qualificationId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{qualificationId}")
    public AjaxResult getInfo(@PathVariable("qualificationId")Long qualificationId) {
        return success(baseEntQualificationService.getById(qualificationId));
    }

    /**
     * 新增企业资质
     */
    @PreAuthorize("@ss.hasPermi('ent:qualification:add')")
    @Log(title = "企业资质", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增企业资质")
    @PostMapping
    public AjaxResult add(@RequestBody BaseEntQualification baseEntQualification) {
        baseEntQualification.setCreateBy(getUsername());
        baseEntQualification.setUpdateBy(getUsername());
        return toAjax(baseEntQualificationService.save(baseEntQualification));
    }

    /**
     * 修改企业资质
     */
    @PreAuthorize("@ss.hasPermi('ent:qualification:edit')")
    @Log(title = "企业资质", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改企业资质")
    @PutMapping
    public AjaxResult edit(@RequestBody BaseEntQualification baseEntQualification) {
        baseEntQualification.setUpdateBy(getUsername());
        return toAjax(baseEntQualificationService.updateById(baseEntQualification));
    }

    /**
     * 删除企业资质
     */
    @PreAuthorize("@ss.hasPermi('ent:qualification:remove')")
    @Log(title = "企业资质", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除企业资质")
    @DeleteMapping("/{qualificationIds}")
    public AjaxResult remove(@PathVariable Long[] qualificationIds) {
        return toAjax(baseEntQualificationService.removeByIds(Arrays.asList(qualificationIds)));
    }
}
