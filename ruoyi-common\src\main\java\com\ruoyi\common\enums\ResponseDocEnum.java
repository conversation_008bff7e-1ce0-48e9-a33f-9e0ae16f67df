package com.ruoyi.common.enums;

/**
 * 响应文件枚举
 *
 * <AUTHOR>
 */
public enum ResponseDocEnum
{
    KBYLB("kbylb", "开标一览表"),
    MXBJB("mxbjb", "明细报价表"),
    FDDBRSFZM("fddbrsfzm", "法定代表人身份证明"),
    FDDBRSQS("fddbrsqs", "法定代表人授权书"),
    ZXQYSMH("zxqysmh", "中小企业声明函"),
    JYQY("jyqy", "监狱企业"),
    YYZZ("yyzz", "营业执照"),
    ZZZM("zzzm", "资质证明"),


    XYZG("xyzg", "信用中国"),
    ZGZXXXGKW("zgzxxxgkw", "中国执行信息公开网"),
    ZGZFCGW("zgzfcgw", "中国政府采购网"),
    QGJZSCJGGGFWPT("qgjzscjgggfwpt", "全国建筑市场监管公共服务平台"),

    YBJGCLQD("ybjgclqd", "已标价工程量清单"),

    SGFAJJSCS("sgfajjscs", "施工方案及技术措施"),
    GCJDJHYCS("gcjdjhycs", "工程进度计划与措施"),
    JNJP("jnjp", "节能减排"),
    XGY("xgy", "新工艺"),
    FXGLCS("fxglcs", "风险管理措施"),

    TXRZ("txrz", "体系认证"),
    NPXMGLRY("npxmglry", "拟派项目管理人员"),
    XMJSFZR("xmjsfzr", "项目技术负责人"),
    LSYJ("lsyj", "类似业绩"),

    JSFA("jsfa", "技术方案"),
    FWFA("fwfa", "服务方案"),
    JSPLB("jsplb", "技术偏离表"),
    NPXMZRYB("npxmzryb", "拟派项目组人员表"),



    RES_DOC_DECISION("resDocDecision", "响应文件校验");

    private final String code;
    private final String info;

    ResponseDocEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
