package com.ruoyi.base.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.base.domain.BaseEntStaff;
import com.ruoyi.base.service.IBaseEntStaffService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 企业员工信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "企业员工信息管理")
@RestController
@RequestMapping("/ent/staff")
public class BaseEntStaffController extends BaseController {
    @Autowired
    private IBaseEntStaffService baseEntStaffService;

/**
 * 查询企业员工信息列表
 */
@PreAuthorize("@ss.hasPermi('ent:staff:list')")
@ApiOperation(value = "查询企业员工信息列表")
@GetMapping("/list")
    public TableDataInfo list(BaseEntStaff baseEntStaff) {
        startPage();
        List<BaseEntStaff> list = baseEntStaffService.selectList(baseEntStaff);
        return getDataTable(list);
    }

    /**
     * 导出企业员工信息列表
     */
    @PreAuthorize("@ss.hasPermi('ent:staff:export')")
    @Log(title = "企业员工信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出企业员工信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseEntStaff baseEntStaff) {
        List<BaseEntStaff> list = baseEntStaffService.selectList(baseEntStaff);
        ExcelUtil<BaseEntStaff> util = new ExcelUtil<BaseEntStaff>(BaseEntStaff. class);
        util.exportExcel(response, list, "企业员工信息数据");
    }

    /**
     * 获取企业员工信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('ent:staff:query')")
    @ApiOperation(value = "获取企业员工信息详细信息")
    @ApiImplicitParam(name = "staffId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{staffId}")
    public AjaxResult getInfo(@PathVariable("staffId")Long staffId) {
        return success(baseEntStaffService.getById(staffId));
    }

    /**
     * 新增企业员工信息
     */
    @PreAuthorize("@ss.hasPermi('ent:staff:add')")
    @Log(title = "企业员工信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增企业员工信息")
    @PostMapping
    public AjaxResult add(@RequestBody BaseEntStaff baseEntStaff) {
        baseEntStaff.setCreateBy(getUsername());
        baseEntStaff.setUpdateBy(getUsername());
        return toAjax(baseEntStaffService.save(baseEntStaff));
    }

    /**
     * 修改企业员工信息
     */
    @PreAuthorize("@ss.hasPermi('ent:staff:edit')")
    @Log(title = "企业员工信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改企业员工信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BaseEntStaff baseEntStaff) {
        baseEntStaff.setUpdateBy(getUsername());
        return toAjax(baseEntStaffService.updateById(baseEntStaff));
    }

    /**
     * 删除企业员工信息
     */
    @PreAuthorize("@ss.hasPermi('ent:staff:remove')")
    @Log(title = "企业员工信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除企业员工信息")
    @DeleteMapping("/{staffIds}")
    public AjaxResult remove(@PathVariable Long[] staffIds) {
        return toAjax(baseEntStaffService.removeByIds(Arrays.asList(staffIds)));
    }
}
