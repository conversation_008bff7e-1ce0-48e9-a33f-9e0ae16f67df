package com.ruoyi.busi.enums;

public enum SystemRoleEnum {
    PURCH<PERSON><PERSON>("purchaser", "采购人", "采购人"),
    SUPPLIER("supplier", "供应商", "供应商"),
    AGENCY("agency", "代理机构", "代理机构"),
    XIAOHE_AI("xiaoheAI", "小鹤智能", "小鹤智能"),
    ADMIN("admin", "管理员", "管理员"),;

    private String code;
    private String name;
    private String remark;

    SystemRoleEnum(String code, String name, String remark) {
        this.code = code;
        this.name = name;
        this.remark = remark;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getRemark() {
        return remark;
    }
}
