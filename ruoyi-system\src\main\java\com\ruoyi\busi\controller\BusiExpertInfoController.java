package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiExpertInfo;
import com.ruoyi.busi.service.IBusiExpertInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * 专家信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Api(tags = "专家信息管理")
@RestController
@RequestMapping("/expert/info")
public class BusiExpertInfoController extends BaseController {
    @Autowired
    private IBusiExpertInfoService busiExpertInfoService;

    /**
     * 查询专家信息列表
     */
    @PreAuthorize("@ss.hasPermi('expert:info:list')")
    @ApiOperation(value = "查询专家信息列表")
    @GetMapping("/list")
    public TableDataInfo list(BusiExpertInfo busiExpertInfo) {
        startPage();
        List<BusiExpertInfo> list = busiExpertInfoService.selectList(busiExpertInfo);
        return getDataTable(list);
    }

    /**
     * 导出专家信息列表
     */
    @PreAuthorize("@ss.hasPermi('expert:info:export')")
    @Log(title = "专家信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExpertInfo busiExpertInfo) {
        List<BusiExpertInfo> list = busiExpertInfoService.selectList(busiExpertInfo);
        ExcelUtil<BusiExpertInfo> util = new ExcelUtil<BusiExpertInfo>(BusiExpertInfo. class);
        util.exportExcel(response, list, "专家信息数据");
    }

    /**
     * 获取专家信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('expert:info:query')")
    @ApiOperation(value = "获取专家信息详细信息")
    @ApiImplicitParam(name = "expertId", value = "专家id", required = true, dataType = "Long")
    @GetMapping(value = "/{expertId}")
    public AjaxResult getInfo(@PathVariable("expertId")Long expertId) {
        return success(busiExpertInfoService.getById(expertId));
    }

    @Log(title = "专家信息", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('expert:info:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BusiExpertInfo> util
                = new ExcelUtil<BusiExpertInfo>(BusiExpertInfo.class);
        List<BusiExpertInfo> list = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = busiExpertInfoService.importExpertInfo(list, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BusiExpertInfo> util = new ExcelUtil<BusiExpertInfo>(BusiExpertInfo.class);
        util.importTemplateExcel(response, "专家信息");
    }

    /**
     * 新增专家信息
     */
    @PreAuthorize("@ss.hasPermi('expert:info:add')")
    @Log(title = "专家信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家信息")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExpertInfo busiExpertInfo) {
        busiExpertInfo.setCreateBy(getUsername());
        busiExpertInfo.setUpdateBy(getUsername());
        return toAjax(busiExpertInfoService.save(busiExpertInfo));
    }

    /**
     * 修改专家信息
     */
    @PreAuthorize("@ss.hasPermi('expert:info:edit')")
    @Log(title = "专家信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExpertInfo busiExpertInfo) {
        busiExpertInfo.setUpdateBy(getUsername());
        return toAjax(busiExpertInfoService.updateById(busiExpertInfo));
    }

    /**
     * 删除专家信息
     */
    @PreAuthorize("@ss.hasPermi('expert:info:remove')")
    @Log(title = "专家信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家信息")
    @DeleteMapping("/{expertIds}")
    public AjaxResult remove(@PathVariable Long[] expertIds) {
        return toAjax(busiExpertInfoService.removeByIds(Arrays.asList(expertIds)));
    }
}
