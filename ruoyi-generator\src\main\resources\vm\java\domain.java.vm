package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.*;


import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
#if($table.crud || $table.sub)
import com.ruoyi.common.core.domain.BaseEntity;
#elseif($table.tree)
import com.ruoyi.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@ApiModel("${functionName}对象")
@TableName(resultMap = "${packageName}.mapper.${ClassName}Mapper.${ClassName}Result")
public class ${ClassName} extends ${Entity} implements Serializable
        {
private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /**
     * $column.columnComment
     */
    @ApiModelProperty("${column.columnComment}")
        #if($column.isPk == 1)
        @TableId(type = IdType.ASSIGN_ID)
        #end
        #if($column.javaField == 'delFlag')
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
        #end
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
            @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
            #else
            @Excel(name = "${comment}")
            #end
        #end
    private $column.javaType $column.javaField;
    #end
#end

#if($table.sub)
/** $table.subTable.functionName信息 */
@TableField(exist = false)
private List<${subClassName}> ${subclassName}List;
#end
        }
