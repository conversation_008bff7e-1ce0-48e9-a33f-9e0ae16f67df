package com.ruoyi.web.controller.home.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TodoVo {

    private String title; // 待办事项标题
    private String description; // 待办事项描述
    private Object data;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate; // 截止日期
    private int priority; // 优先级（可以是整数值，例如1表示最高优先级）
    private int type;

}
