package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 投标记录对象 busi_bidding_record
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ApiModel("投标记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiBiddingRecordMapper.BusiBiddingRecordResult")
public class BusiBiddingRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 投标记录id
     */
    @ApiModelProperty("投标记录id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long biddingId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 投标人id
     */
    @ApiModelProperty("投标人id")
    @Excel(name = "投标人id")
    private Long bidderId;
    /**
     * 投标人名称
     */
    @ApiModelProperty("投标人名称")
    @Excel(name = "投标人名称")
    private String bidderName;
    /**
     * 投标人代码
     */
    @ApiModelProperty("投标人代码")
    @Excel(name = "投标人代码")
    private String bidderCode;
    /**
     * 上传ip
     */
    @ApiModelProperty("上传ip")
    @Excel(name = "上传ip")
    private String uploadIp;
    /**
     * 上传时间
     */
    @ApiModelProperty("上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;
    /**
     * 取消时间
     */
    @ApiModelProperty("取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cancelTime;
    /**
     * 删除标记 0正常 1删除
     */
    @ApiModelProperty("删除标记 0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @ApiModelProperty("投标金额")
    @Excel(name = "投标金额")
    private BigDecimal bidAmount;

    @ApiModelProperty("报价单位")
    @Excel(name = "报价单位")
    private String priceUnit;
    /**
     * 采购文件版本
     */
    @ApiModelProperty("采购文件版本")
    @Excel(name = "采购文件版本")
    private Integer noticeVersion;

    /* 非数据库字段  */
    @TableField(exist = false)
    private String priceUnitLabel;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<BusiAttachment> attachments;

    @TableField(exist = false)
    private BusiTenderProject  project;


    @TableField(exist = false)
    private BusiBidderInfo  busiBidderInfo;

    @TableField(exist = false)
    private Integer signInStatus;
    //g供应商输入解密密码
    @TableField(exist = false)
    private String supplierKey;

}
