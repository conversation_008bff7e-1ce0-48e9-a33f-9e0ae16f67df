package com.ruoyi.common.enums;

/**
 * 评分办法类别
 * 
 * <AUTHOR>
 */
public enum ProcurementDocumentsType
{
    ITEM_GCKBYLB("gckbylb", "工程开标一览表"),
    ITEM_KBYLB("kbylb", "开标一览表"),
    ITEM_ZXQYSMH("zxqysmh", "中小企业声明函");

    private final String code;
    private final String info;

    ProcurementDocumentsType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
