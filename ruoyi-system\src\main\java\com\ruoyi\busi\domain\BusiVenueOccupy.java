package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 场地占用对象 busi_venue_occupy
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("场地占用对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiVenueOccupyMapper.BusiVenueOccupyResult")
public class BusiVenueOccupy extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 场地占用id
     */
    @ApiModelProperty("场地占用id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long occupyId;
    /**
     * 公告id
     */
    @ApiModelProperty("公告id")
    @Excel(name = "公告id")
    private Long noticeId;
    /**
     * 场地id
     */
    @ApiModelProperty("场地id")
    @Excel(name = "场地id")
    private Long venueId;
    /**
     * 场地名称
     */
    @ApiModelProperty("场地名称")
    @Excel(name = "场地名称")
    private String venueName;
    /**
     * 场地类型 (1开标 2评标)
     */
    @ApiModelProperty("场地类型 (1开标 2评标)")
    @Excel(name = "场地类型 (1开标 2评标)")
    private Integer venueType;
    /**
     * 占用开始时间
     */
    @ApiModelProperty("占用开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "占用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date occupyStartTime;
    /**
     * 占用结束时间
     */
    @ApiModelProperty("占用结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "占用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date occupyEndTime;
    /**
     * 人数
     */
    @ApiModelProperty("人数")
    @Excel(name = "人数")
    private Long peopleNumber;
    /**
     * 删除标记 (0正常 1删除)
     */
    @ApiModelProperty("删除标记 (0正常 1删除)")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    /**
     * 评标时间段0上午1下午2全天
     */
    @ApiModelProperty("评标时间段0上午1下午2全天")
    @Excel(name = "评标时间段0上午1下午2全天")
    private Integer bidEvaluationPeriod;

}
