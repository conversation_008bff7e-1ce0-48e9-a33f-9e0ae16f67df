package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 在线开标消息记录对象 busi_open_message_record
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
@Data
@ApiModel("在线开标消息记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiOpenMessageRecordMapper.BusiOpenMessageRecordResult")
public class BusiOpenMessageRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息id
     */
    @ApiModelProperty("消息id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 发送者id
     */
    @ApiModelProperty("发送者id")
    @Excel(name = "发送者id")
    private Long sendId;


    /**
     * 发送者id
     */
    @TableField(exist = false)
    @ApiModelProperty("发送者名称")
    @Excel(name = "发送者名称")
    private String sendName;
    /**
     * 接收者id
     */
    @ApiModelProperty("接收者id")
    @Excel(name = "接收者id")
    private Long projectId;
    /**
     * 发送内容
     */
    @ApiModelProperty("发送内容")
    @Excel(name = "发送内容")
    private String content;
    /**
     * 消息内容类型 IMCmdType
     * 0 登录，1心跳，2强制下线，3私聊，4群发
     */
    @ApiModelProperty("消息内容类型 IMCmdType")
    @Excel(name = "消息内容类型 IMCmdType")
    private Integer type;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private Integer status;
    /**
     * 发送时间
     */
    @ApiModelProperty("发送时间")
    @Excel(name = "发送时间")
    private Date sendTime;

    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @TableField()
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private List<BaseEntInfo> baseEntInfos;


}
