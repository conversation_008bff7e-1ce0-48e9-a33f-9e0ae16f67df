package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiWinningBidderAdviceNote;
import com.ruoyi.busi.service.IBusiWinningBidderAdviceNoteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 成交通知书Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Api(tags = "成交通知书管理")
@RestController
@RequestMapping("/bidder/advice")
public class BusiWinningBidderAdviceNoteController extends BaseController {
    @Autowired
    private IBusiWinningBidderAdviceNoteService busiWinningBidderAdviceNoteService;

/**
 * 查询成交通知书列表
 */
@PreAuthorize("@ss.hasPermi('bidder:advice:list')")
@ApiOperation(value = "查询成交通知书列表")
@GetMapping("/list")
    public TableDataInfo list(BusiWinningBidderAdviceNote busiWinningBidderAdviceNote) {
        startPage();
        List<BusiWinningBidderAdviceNote> list = busiWinningBidderAdviceNoteService.selectList(busiWinningBidderAdviceNote);
        return getDataTable(list);
    }

    /**
     * 导出成交通知书列表
     */
    @PreAuthorize("@ss.hasPermi('bidder:advice:export')")
    @Log(title = "成交通知书", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出成交通知书列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiWinningBidderAdviceNote busiWinningBidderAdviceNote) {
        List<BusiWinningBidderAdviceNote> list = busiWinningBidderAdviceNoteService.selectList(busiWinningBidderAdviceNote);
        ExcelUtil<BusiWinningBidderAdviceNote> util = new ExcelUtil<BusiWinningBidderAdviceNote>(BusiWinningBidderAdviceNote. class);
        util.exportExcel(response, list, "成交通知书数据");
    }

    /**
     * 获取成交通知书详细信息
     */
    @PreAuthorize("@ss.hasPermi('bidder:advice:query')")
    @ApiOperation(value = "获取成交通知书详细信息")
    @ApiImplicitParam(name = "noteId", value = "通知书id", required = true, dataType = "Long")
    @GetMapping(value = "/{noteId}")
    public AjaxResult getInfo(@PathVariable("noteId")Long noteId) {
        return success(busiWinningBidderAdviceNoteService.selecInfoIncludeAttachments(noteId));
    }

    /**
     * 新增成交通知书
     */
    @PreAuthorize("@ss.hasPermi('bidder:advice:add')")
    @Log(title = "成交通知书", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增成交通知书")
    @PostMapping
    public AjaxResult add(@RequestBody BusiWinningBidderAdviceNote busiWinningBidderAdviceNote) {
        return toAjax(busiWinningBidderAdviceNoteService.save(busiWinningBidderAdviceNote));
    }

    /**
     * 修改成交通知书
     */
    @PreAuthorize("@ss.hasPermi('bidder:advice:edit')")
    @Log(title = "成交通知书", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改成交通知书")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiWinningBidderAdviceNote busiWinningBidderAdviceNote) {
        return toAjax(busiWinningBidderAdviceNoteService.updateById(busiWinningBidderAdviceNote));
    }

    /**
     * 删除成交通知书
     */
    @PreAuthorize("@ss.hasPermi('bidder:advice:remove')")
    @Log(title = "成交通知书", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除成交通知书")
    @DeleteMapping("/{noteIds}")
    public AjaxResult remove(@PathVariable Long[] noteIds) {
        return toAjax(busiWinningBidderAdviceNoteService.removeByIds(Arrays.asList(noteIds)));
    }


    /**
     * 新增成交通知书
     */
    @PreAuthorize("@ss.hasPermi('bidder:advice:edit')")
    @Log(title = "成交通知书", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "提交成交通知书")
    @PostMapping("/busiProcess")
    public AjaxResult busiProcess(@RequestBody BusiWinningBidderAdviceNote busiWinningBidderAdviceNote) {
        if (busiWinningBidderAdviceNoteService.audit(busiWinningBidderAdviceNote)) {
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }
}
