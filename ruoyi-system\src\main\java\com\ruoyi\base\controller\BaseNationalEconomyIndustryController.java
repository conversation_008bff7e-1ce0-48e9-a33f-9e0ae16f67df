package com.ruoyi.base.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.base.domain.BaseNationalEconomyIndustry;
import com.ruoyi.base.service.IBaseNationalEconomyIndustryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 国民经济行业分类Controller
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/economy/industry")
public class BaseNationalEconomyIndustryController extends BaseController
{
    @Autowired
    private IBaseNationalEconomyIndustryService baseNationalEconomyIndustryService;

    /**
     * 查询国民经济行业分类列表
     */
    @PreAuthorize("@ss.hasPermi('economy:industry:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        startPage();
        List<BaseNationalEconomyIndustry> list = baseNationalEconomyIndustryService.selectBaseNationalEconomyIndustryList(baseNationalEconomyIndustry);
        return getDataTable(list);
    }

    /**
     * 导出国民经济行业分类列表
     */
    @PreAuthorize("@ss.hasPermi('economy:industry:export')")
    @Log(title = "国民经济行业分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        List<BaseNationalEconomyIndustry> list = baseNationalEconomyIndustryService.selectBaseNationalEconomyIndustryList(baseNationalEconomyIndustry);
        ExcelUtil<BaseNationalEconomyIndustry> util = new ExcelUtil<BaseNationalEconomyIndustry>(BaseNationalEconomyIndustry.class);
        util.exportExcel(response, list, "国民经济行业分类数据");
    }

    /**
     * 获取国民经济行业分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('economy:industry:query')")
    @GetMapping(value = "/{industrCode}")
    public AjaxResult getInfo(@PathVariable("industrCode") String industrCode)
    {
        return success(baseNationalEconomyIndustryService.selectBaseNationalEconomyIndustryByIndustrCode(industrCode));
    }

    /**
     * 新增国民经济行业分类
     */
    @PreAuthorize("@ss.hasPermi('economy:industry:add')")
    @Log(title = "国民经济行业分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        baseNationalEconomyIndustry.setCreateBy(getUsername());
        return toAjax(baseNationalEconomyIndustryService.insertBaseNationalEconomyIndustry(baseNationalEconomyIndustry));
    }

    /**
     * 修改国民经济行业分类
     */
    @PreAuthorize("@ss.hasPermi('economy:industry:edit')")
    @Log(title = "国民经济行业分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        baseNationalEconomyIndustry.setUpdateBy(getUsername());
        return toAjax(baseNationalEconomyIndustryService.updateBaseNationalEconomyIndustry(baseNationalEconomyIndustry));
    }

    /**
     * 删除国民经济行业分类
     */
    @PreAuthorize("@ss.hasPermi('economy:industry:remove')")
    @Log(title = "国民经济行业分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{industrCodes}")
    public AjaxResult remove(@PathVariable String[] industrCodes)
    {
        return toAjax(baseNationalEconomyIndustryService.deleteBaseNationalEconomyIndustryByIndustrCodes(industrCodes));
    }
}
