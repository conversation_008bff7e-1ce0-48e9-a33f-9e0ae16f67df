package com.ruoyi.busi.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.busi.domain.BusiExtractExpertApply;
import com.ruoyi.busi.service.IBusiExtractExpertApplyService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.utils.RestExtractParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 专家抽取申请Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "专家抽取申请管理")
@RestController
@RequestMapping("/expert/apply")
public class BusiExtractExpertApplyController extends BaseController {
    @Autowired
    private IBusiExtractExpertApplyService busiExtractExpertApplyService;

/**
 * 查询专家抽取申请列表
 */
@PreAuthorize("@ss.hasPermi('expert:apply:list')")
@ApiOperation(value = "查询专家抽取申请列表")
@GetMapping("/list")
    public TableDataInfo list(BusiExtractExpertApply busiExtractExpertApply) {
        startPage();
        List<BusiExtractExpertApply> list = busiExtractExpertApplyService.selectList(busiExtractExpertApply);
        return getDataTable(list);
    }

    /**
     * 导出专家抽取申请列表
     */
    @PreAuthorize("@ss.hasPermi('expert:apply:export')")
    @Log(title = "专家抽取申请", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家抽取申请列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExtractExpertApply busiExtractExpertApply) {
        List<BusiExtractExpertApply> list = busiExtractExpertApplyService.selectList(busiExtractExpertApply);
        ExcelUtil<BusiExtractExpertApply> util = new ExcelUtil<BusiExtractExpertApply>(BusiExtractExpertApply. class);
        util.exportExcel(response, list, "专家抽取申请数据");
    }

    /**
     * 获取专家抽取申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('expert:apply:query')")
    @ApiOperation(value = "获取专家抽取申请详细信息")
    @ApiImplicitParam(name = "applyId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{applyId}")
    public AjaxResult getInfo(@PathVariable("applyId")Long applyId) {
        return success(busiExtractExpertApplyService.getById(applyId));
    }

    /**
     * 新增专家抽取申请
     */
    @PreAuthorize("@ss.hasPermi('expert:apply:add')")
    @Log(title = "专家抽取申请", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家抽取申请")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExtractExpertApply busiExtractExpertApply) throws IOException {
        busiExtractExpertApply.setCreateBy(getUsername());
        busiExtractExpertApply.setUpdateBy(getUsername());
        busiExtractExpertApplyService.saveHaveExtract(busiExtractExpertApply);
        return AjaxResult.success();
    }


    /**
     * 专家抽取
     */
    @PreAuthorize("@ss.hasPermi('expert:apply:extract')")
    @Log(title = "专家抽取", businessType = BusinessType.OTHER)
    @ApiOperation(value = "专家抽取")
    @PostMapping("/{applyIds}")
    public AjaxResult extract(@PathVariable Long[] applyIds) {
        busiExtractExpertApplyService.extract(Arrays.asList(applyIds),false);
        return AjaxResult.success();
    }

    /**
     * 修改专家抽取申请
     */
    @PreAuthorize("@ss.hasPermi('expert:apply:edit')")
    @Log(title = "专家抽取申请", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家抽取申请")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExtractExpertApply busiExtractExpertApply) throws IOException {
        busiExtractExpertApply.setUpdateBy(getUsername());
        busiExtractExpertApplyService.saveHaveExtract(busiExtractExpertApply);
        return AjaxResult.success();
    }

    /**
     * 删除专家抽取申请
     */
    @PreAuthorize("@ss.hasPermi('expert:apply:remove')")
    @Log(title = "专家抽取申请", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家抽取申请")
    @DeleteMapping("/{applyIds}")
    public AjaxResult remove(@PathVariable Long applyIds) {
        boolean r = busiExtractExpertApplyService.removeApply(applyIds);
        return toAjax(r);
    }



    /**
     * 删除专家抽取申请
     */
    @ApiOperation(value = "重新抽取")
    @PostMapping("/resetExtract")
    public AjaxResult resetExtract(@RequestBody RestExtractParam restExtractParam) throws IOException {
        Map<String, Object> map = new HashMap<>();
//        map.put("expIds",expIds);//自主抽取专家ids
//        map.put("projectExpertId",thirdId);//专家组重新抽取直接传抽取thirdId
//        map.put("thirdId",thirdId);//重新抽取id
//        map.put("applyId",applyId);//申请id
        System.out.println(JSONObject.toJSONString(restExtractParam));
        return busiExtractExpertApplyService.resetExtract(restExtractParam);    }
}
