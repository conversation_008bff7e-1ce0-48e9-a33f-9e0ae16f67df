package com.ruoyi.busi.controller;

import java.io.IOException;
import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiCancelProject;
import com.ruoyi.busi.service.IBusiCancelProjectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 取消采购项目Controller
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Api(tags = "取消采购项目管理")
@RestController
@RequestMapping("/cancel/project")
public class BusiCancelProjectController extends BaseController {
    @Autowired
    private IBusiCancelProjectService busiCancelProjectService;
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;

/**
 * 查询取消采购项目列表
 */
@PreAuthorize("@ss.hasPermi('cancel:project:list')")
@ApiOperation(value = "查询取消采购项目列表")
@GetMapping("/list")
    public TableDataInfo list(BusiCancelProject busiCancelProject) {
        startPage();
        List<BusiCancelProject> list = busiCancelProjectService.selectList(busiCancelProject);
        return getDataTable(list);
    }

    /**
     * 导出取消采购项目列表
     */
    @PreAuthorize("@ss.hasPermi('cancel:project:export')")
    @Log(title = "取消采购项目", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出取消采购项目列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiCancelProject busiCancelProject) {
        List<BusiCancelProject> list = busiCancelProjectService.selectList(busiCancelProject);
        ExcelUtil<BusiCancelProject> util = new ExcelUtil<BusiCancelProject>(BusiCancelProject. class);
        util.exportExcel(response, list, "取消采购项目数据");
    }

    /**
     * 获取取消采购项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('cancel:project:query')")
    @ApiOperation(value = "获取取消采购项目详细信息")
    @ApiImplicitParam(name = "cancelId", value = "取消id", required = true, dataType = "Long")
    @GetMapping(value = "/{cancelId}")
    public AjaxResult getInfo(@PathVariable("cancelId")Long cancelId) {
        return busiCancelProjectService.getCancelProjectById(cancelId);
    }

    @GetMapping(value = "/createContent")
    public String createContent(Long cancelId) {
        BusiCancelProject cancelProject = busiCancelProjectService.getById(cancelId);
        BusiTenderProject project = iBusiTenderProjectService.getById(cancelProject.getProjectId());
        return busiCancelProjectService.createContent(cancelProject, project);
    }

    /**
     * 新增取消采购项目
     */
    @PreAuthorize("@ss.hasPermi('cancel:project:add')")
    @Log(title = "取消采购项目", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增取消采购项目")
    @PostMapping
    public AjaxResult add(@RequestBody BusiCancelProject busiCancelProject) throws IOException {
        busiCancelProject.setCancelStatus(0);
        return busiCancelProjectService.saveCancelProject(busiCancelProject);
    }


    /**
     * 新增取消采购项目
     */
    @PreAuthorize("@ss.hasPermi('cancel:project:audit')")
    @Log(title = "取消采购项目", businessType = BusinessType.INSERT)
    @ApiOperation(value = "取消采购项目审批")
    @RequestMapping(value = "/approveRequest")
    public AjaxResult approveRequest(@RequestBody BusiCancelProject busiCancelProject) throws IOException {
        return busiCancelProjectService.approveRequest(busiCancelProject);
    }




    /**
     * 修改取消采购项目
     */
    @PreAuthorize("@ss.hasPermi('cancel:project:edit')")
    @Log(title = "取消采购项目", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改取消采购项目")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiCancelProject busiCancelProject) {
        return toAjax(busiCancelProjectService.updateById(busiCancelProject));
    }

    /**
     * 删除取消采购项目
     */
    @PreAuthorize("@ss.hasPermi('cancel:project:remove')")
    @Log(title = "取消采购项目", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除取消采购项目")
    @DeleteMapping("/{cancelIds}")
    public AjaxResult remove(@PathVariable Long[] cancelIds) {
        return toAjax(busiCancelProjectService.removeByIds(Arrays.asList(cancelIds)));
    }
}
