package com.ruoyi.busi.enums;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPObject;

public enum AuditBusiTypeEnum {
    TENDER_INTENTION("0", "采购意向", 0, new String[]{"xiaoheAI"}),
    TENDER_PROJECT("10", "采购项目", 0, new String[]{"xiaoheAI"}),
    TENDER_NOTICE("20", "采购公告", 0, new String[]{"xiaoheAI"}),
    EXTRACT_EXPERT("30", "专家抽取申请", 0, new String[]{"xiaoheAI"}),
    BIDDING_RECORD("999", "上传响应文件", 0, new String[]{"xiaoheAI"}),
    BID_OPENING("40", "开标情况", 0, new String[]{"xiaoheAI"}),
    BID_EVALUATION("50", "评审情况", 0, new String[]{"xiaoheAI"}),
    WINNING_BIDDER_NOTICE("60", "成交结果公告", 0, new String[]{"xiaoheAI"}),
    WINNING_BIDDER_ADVICE_NOTE("70", "成交通知书", 0, new String[]{"xiaoheAI"}),
    TRANSACTION_CONTRACT("80", "合同", 0, new String[]{"xiaoheAI"}),
    CANCEL_PROJECT("-1", "取消项目公告", 1, new String[]{"xiaoheAI"}),
    PROCESS_INFO("200", "归档", 0, new String[]{"xiaoheAI"}),
    ENT_INFO("300", "企业信息", 1, new String[]{"xiaoheAI"});

    private String code;
    private String name;
    private int processNum;
    private String[] process;

    AuditBusiTypeEnum(String code, String name, int processNum, String[] process) {
        this.code = code;
        this.name = name;
        this.process = process;
        this.processNum = processNum;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String[] getProcess() {
        return process;
    }

    public int getProcessNum() {
        return processNum;
    }
}
