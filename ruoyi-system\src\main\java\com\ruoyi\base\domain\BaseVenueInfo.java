package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 场地信息对象 base_venue_info
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("场地信息对象")
@TableName(resultMap = "com.ruoyi.base.mapper.BaseVenueInfoMapper.BaseVenueInfoResult")
public class BaseVenueInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 场地id
     */
    @ApiModelProperty("场地id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long venueId;
    /**
     * 场地名称
     */
    @ApiModelProperty("场地名称")
    @Excel(name = "场地名称")
    private String venueName;
    /**
     * 场地类型 (1开标 2评标)
     */
    @ApiModelProperty("场地类型 (1开标 2评标)")
    @Excel(name = "场地类型 (1开标 2评标)")
    private String venueType;
    /**
     * 场地规格
     */
    @ApiModelProperty("场地规格")
    @Excel(name = "场地规格")
    private String venueSpec;
    /**
     * 删除标记 (0正常 1删除)
     */
    @ApiModelProperty("删除标记 (0正常 1删除)")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}

