package com.ruoyi.busi.domain.vo;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;


@Data
@JacksonXmlRootElement(localName="root")
public class XmlToEntityVo {

    /**
     * version : 1
     * projectInfos : {"projectInfo":[{"code":"projectName","name":"项目名称","value":"xxx项目"},{"code":"projectCode","name":"项目编号","value":"xxx"}]}
     * bidInfos : {"bidInfo":[{"code":"bidder","name":"投标人","remark":"投标企业名称","value":"供应商1号"},{"code":"bidManager","name":"项目负责人","value":"张三"},{"code":"bidderLegal","name":"法人或其授权人","value":"张三"},{"code":"bidPrice","name":"投标报价","remark":"只能输入金额数字，不要带单位","unit":"元","value":100}]}
     * bidAnnounces : {"bidAnnounce":[{"code":"bidder","name":"投标人","remark":"投标企业名称","value":"供应商1号"},{"code":"bidManager","name":"项目负责人","value":"张三"},{"code":"bidderLegal","name":"法人或其授权人","value":"张三"},{"code":"bidPrice","name":"投标报价","remark":"只能输入金额数字，不要带单位","unit":"元","value":100}]}
     */
    @JacksonXmlProperty(localName = "version",isAttribute = true)
    private String version;

    @JacksonXmlElementWrapper(namespace = "ProjectInfoBean",localName = "projectInfos",useWrapping = true)
    @JacksonXmlProperty(localName = "projectInfo")
    private List<ProjectInfoBean> projectInfos;
    @JacksonXmlElementWrapper(namespace = "BidInfoBean",localName = "infoBeans",useWrapping = true)
    @JacksonXmlProperty(localName = "infoBean")
    private List<BidInfoBean> infoBeans;
    @JacksonXmlElementWrapper(namespace = "BidAnnounceBean",localName = "bidAnnounceBeans",useWrapping = true)
    @JacksonXmlProperty(localName = "bidAnnounceBean")
    private List<BidAnnounceBean> bidAnnounceBeans;
    @Data
    @Accessors(chain = true)
    public static class ProjectInfoBean {
        /**
         * code : projectName
         * name : 项目名称
         * value : xxx项目
         */
        @JacksonXmlProperty(localName = "code",isAttribute = true)
        private String code;
        @JacksonXmlProperty(localName = "name",isAttribute = true)
        private String name;
        @JacksonXmlProperty(localName = "value",isAttribute = true)
        private String value;
        @JacksonXmlProperty(localName = "remark",isAttribute = true)
        private String remark;
    }

    @Data
    @Accessors(chain = true)
    public static class BidInfoBean {
        /**
         * code : bidder
         * name : 投标人
         * remark : 投标企业名称
         * value : 供应商1号
         * unit : 元
         */
        @JacksonXmlProperty(localName = "code",isAttribute = true)
        private String code;
        @JacksonXmlProperty(localName = "name",isAttribute = true)
        private String name;
        @JacksonXmlProperty(localName = "remark",isAttribute = true)
        private String remark;
        @JacksonXmlProperty(localName = "value",isAttribute = true)
        private String value;
//        @JacksonXmlProperty(localName = "unit",isAttribute = true)
//        private String unit;
    }
    @Data
    @Accessors(chain = true)
    public static class BidAnnounceBean {
        /**
         * code : bidder
         * name : 投标人
         * remark : 投标企业名称
         * value : 供应商1号
         * unit : 元
         */
        @JacksonXmlProperty(localName = "code",isAttribute = true)
        private String code;
        @JacksonXmlProperty(localName = "name",isAttribute = true)
        private String name;
        @JacksonXmlProperty(localName = "remark",isAttribute = true)
        private String remark;
        @JacksonXmlProperty(localName = "value",isAttribute = true)
        private String value;
        @JacksonXmlProperty(localName = "unit",isAttribute = true)
        private String unit;
    }
    public static <T> T convertXmlToObject(String xml, Class<T> beanType){
        if(null!=xml&&!"".equals(xml)){
            try {
                return new XmlMapper().readValue(xml,beanType);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public JSONObject toVO(){
        JSONObject json=new JSONObject();
        for (BidAnnounceBean bidAnnounceBean : this.bidAnnounceBeans) {
            json.put(bidAnnounceBean.getCode(),bidAnnounceBean.getValue());
        }
        return json;
    }

    public static void main(String[] args) {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" +
                "<root version=\"1\">\n" +
//
                "    <projectInfos>\n" +
                "        <projectInfo code=\"projectName\" name=\"项目名称\" value=\"xxx项目\" remark=\"\"></projectInfo>\n" +
                "        <projectInfo code=\"projectCode\" name=\"项目编号\" value=\"xxx\" remark=\"\"></projectInfo>\n" +
                "    </projectInfos>\n" +
                "    <bidInfos>\n" +
                "        <bidInfo code=\"bidder\" name=\"投标人\" value=\"供应商1号\" remark=\"投标企业名称\"></bidInfo>\n" +
                "        <bidInfo code=\"bidManager\" name=\"项目负责人\" value=\"张三\" remark=\"\"></bidInfo>\n" +
                "        <bidInfo code=\"bidderLegal\" name=\"法人或其授权人\" value=\"张三\" remark=\"\"></bidInfo>\n" +
                "        <bidInfo code=\"bidPrice\" name=\"投标报价\" value=\"100\" unit=\"元\" remark=\"只能输入金额数字，不要带单位\"></bidInfo>\n" +
                "    </bidInfos>\n" +
                "    <bidAnnounces>\n" +
                "        <bidAnnounce code=\"bidder\" name=\"投标人\" value=\"供应商1号\" unit=\"\" remark=\"投标企业名称\"></bidAnnounce>\n" +
                "        <bidAnnounce code=\"bidManager\" name=\"项目负责人\" value=\"张三\" unit=\"\" remark=\"\"></bidAnnounce>\n" +
                "        <bidAnnounce code=\"bidderLegal\" name=\"法人或其授权人\" value=\"张三\" unit=\"\" remark=\"\"></bidAnnounce>\n" +
                "        <bidAnnounce code=\"bidPrice\" name=\"投标报价\" value=\"100\" unit=\"元\" remark=\"只能输入金额数字，不要带单位\"></bidAnnounce>\n" +
                "    </bidAnnounces>\n" +
                "</root>";
        XmlToEntityVo group = convertXmlToObject(xml, XmlToEntityVo.class);
        System.out.println(JSONObject.toJSONString(group.getBidAnnounceBeans()));
    }
}
