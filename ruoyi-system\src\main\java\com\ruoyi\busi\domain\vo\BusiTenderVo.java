package com.ruoyi.busi.domain.vo;

import com.ruoyi.busi.domain.*;
import lombok.Data;

import java.util.List;

@Data
public class BusiTenderVo {

    private BusiTenderIntention tenderIntention;
    private BusiTenderNotice tenderNotice;
    private BusiTenderNotice oldTenderNotice;
    private List<BusiTenderNotice> changeNotice;
    private BusiTenderProject tenderProject;
    private BusiWinningBidderNotice winningBidderNotice;

    private int biddingRecordNum;
    private List<BusiBiddingRecord> biddingRecordList;


}
