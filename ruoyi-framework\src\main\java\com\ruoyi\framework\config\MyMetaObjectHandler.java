package com.ruoyi.framework.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        Date date = new Date();
        String username = userName(metaObject);
        this.strictInsertFill(metaObject, "createTime", Date.class, date);
        this.strictInsertFill(metaObject, "createBy", String.class, username);
        this.strictInsertFill(metaObject, "updateTime", Date.class, date);
        this.strictInsertFill(metaObject, "updateBy", String.class, username);
        this.strictInsertFill(metaObject, "delFlag", Integer.class, 0);
    }

    private String userName(MetaObject metaObject) {
        Map<String, Object> ub = (Map<String, Object>)metaObject.getValue("params");
        if (ub != null && ub.containsKey("systemQuartz")) {
            return "system";
        }else if (ub != null && ub.containsKey("opUser")) {
            return (String) ub.get("opUser");
        }else{
            return SecurityUtils.getUsername();
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        String username = userName(metaObject);
        Date date = new Date();
        this.setFieldValByName("updateTime", date, metaObject);
        this.setFieldValByName("updateBy", username, metaObject);
        System.out.println(metaObject);
    }
}