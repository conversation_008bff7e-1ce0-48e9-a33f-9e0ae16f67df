package com.ruoyi.base.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 多级数据Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "多级数据管理")
@RestController
@RequestMapping("/tree/data")
public class BaseTreeDataController extends BaseController {
    @Autowired
    private IBaseTreeDataService baseTreeDataService;

    /**
     * 查询多级数据列表
     */
    @ApiOperation(value = "查询多级数据列表")
    @GetMapping("/list")
    public TableDataInfo list(BaseTreeData baseTreeData) {
        startPage();
        List<BaseTreeData> list = baseTreeDataService.selectList(baseTreeData);
        return getDataTable(list);
    }

    /**
     * 查询多级数据列表
     */
    @ApiOperation(value = "查询多级数据列表(树)")
    @GetMapping("/listWithTree")
    public AjaxResult listWithTree(BaseTreeData baseTreeData) {
        List<BaseTreeData> list = baseTreeDataService.selectListWithTree(baseTreeData);
        return AjaxResult.success(list);
    }

    /**
     * 导出多级数据列表
     */
    @PreAuthorize("@ss.hasPermi('tree:data:export')")
    @Log(title = "多级数据", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出多级数据列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseTreeData baseTreeData) {
        List<BaseTreeData> list = baseTreeDataService.selectList(baseTreeData);
        ExcelUtil<BaseTreeData> util = new ExcelUtil<BaseTreeData>(BaseTreeData.class);
        util.exportExcel(response, list, "多级数据数据");
    }

    /**
     * 获取多级数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('tree:data:query')")
    @ApiOperation(value = "获取多级数据详细信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataType = "Long")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(baseTreeDataService.getById(id));
    }

    /**
     * 新增多级数据
     */
    @PreAuthorize("@ss.hasPermi('tree:data:add')")
    @Log(title = "多级数据", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增多级数据")
    @PostMapping
    @CacheEvict(value = {"baseTreeData"}, allEntries = true)
    public AjaxResult add(@RequestBody BaseTreeData baseTreeData) {
        baseTreeData.setCreateBy(getUsername());
        baseTreeData.setUpdateBy(getUsername());
        return toAjax(baseTreeDataService.save(baseTreeData));
    }

    /**
     * 修改多级数据
     */
    @PreAuthorize("@ss.hasPermi('tree:data:edit')")
    @Log(title = "多级数据", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改多级数据")
    @PutMapping
    @CacheEvict(value = {"baseTreeData"}, allEntries = true)
    public AjaxResult edit(@RequestBody BaseTreeData baseTreeData) {
        baseTreeData.setUpdateBy(getUsername());
        return toAjax(baseTreeDataService.updateById(baseTreeData));
    }

    /**
     * 删除多级数据
     */
    @PreAuthorize("@ss.hasPermi('tree:data:remove')")
    @Log(title = "多级数据", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除多级数据")
    @DeleteMapping("/{ids}")
    @CacheEvict(value = {"baseTreeData"}, allEntries = true)
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(baseTreeDataService.removeByIds(Arrays.asList(ids)));
    }
}
