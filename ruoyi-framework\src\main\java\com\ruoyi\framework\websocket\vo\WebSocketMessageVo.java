package com.ruoyi.framework.websocket.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.eval.domain.EvalInquiringBidInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.websocket.server.PathParam;
@Data
public class WebSocketMessageVo {
//    @PathParam(value = "userId") String userId,
//    @PathParam(value = "projectId") String projectId,
//    @PathParam(value = "type") Integer type,

    //发送者id  --userId
    public   String sendId;
    //接收者id
    public  String projectId;
    //0是开标室1是询标
    public Integer type;

    //项目评审信息id
    public Long projectEvaluationId;
    // 企业id
    public Long entId;
    //专家id
    public Long expertResultId;
    //消息内容
    public  String message;
    //询标实体
    public EvalInquiringBidInfo evalInquiringBidInfo;
}
