package com.ruoyi.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
        import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.base.mapper.BaseEntStaffMapper;
import com.ruoyi.base.domain.BaseEntStaff;
import com.ruoyi.base.service.IBaseEntStaffService;

/**
 * 企业员工信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BaseEntStaffServiceImpl extends ServiceImpl<BaseEntStaffMapper, BaseEntStaff> implements IBaseEntStaffService {
    /**
     * 查询企业员工信息列表
     *
     * @param baseEntStaff 企业员工信息
     * @return 企业员工信息
     */
    @Override
    public List<BaseEntStaff> selectList(BaseEntStaff baseEntStaff) {
        QueryWrapper<BaseEntStaff> baseEntStaffQueryWrapper = new QueryWrapper<>();
                        baseEntStaffQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntStaff.getEntId()),"ent_id",baseEntStaff.getEntId());
                        baseEntStaffQueryWrapper.like(ObjectUtil.isNotEmpty(baseEntStaff.getStaffName()),"staff_name",baseEntStaff.getStaffName());
                        baseEntStaffQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntStaff.getStaffCode()),"staff_code",baseEntStaff.getStaffCode());
                        baseEntStaffQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntStaff.getStaffSex()),"staff_sex",baseEntStaff.getStaffSex());
                        baseEntStaffQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntStaff.getStaffPhone()),"staff_phone",baseEntStaff.getStaffPhone());
                        baseEntStaffQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntStaff.getStaffDegree()),"staff_degree",baseEntStaff.getStaffDegree());
        return list(baseEntStaffQueryWrapper);
    }
}
