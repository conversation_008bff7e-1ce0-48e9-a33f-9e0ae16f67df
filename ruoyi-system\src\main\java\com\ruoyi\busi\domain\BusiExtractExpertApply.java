package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.busi.domain.vo.Yzpw;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 专家抽取申请对象 busi_extract_expert_apply
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("专家抽取申请对象")
@TableName("busi_extract_expert_apply")
public class BusiExtractExpertApply extends BaseEntity implements Serializable
{
    private static final long serialVersionUID=1L;

    /**
     * 申请id
     */
    @ApiModelProperty("申请id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long applyId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 处理状态0待提交1已提交2已抽取
     */
    @ApiModelProperty("处理状态0待提交1已提交2已抽取")
    @Excel(name = "处理状态0待提交1已提交2已抽取")
    private Integer applyStatus;

    /**
     * 抽取方式0随机1自主
     */
    @ApiModelProperty("抽取方式0随机1自主")
    @Excel(name = "抽取方式0随机1自主")
    private Integer applyMethod;
    /**
     * 抽取方式0随机1自主
     */
    @ApiModelProperty("0论证(建项目)1评审（评审项目）")
    @Excel(name = "抽取类型0论证(建项目)1评审（评审项目）")
    private Integer extractionType;

    //选择时间段，如果是论证，就按这个时间，如果是评审就按评标室的时间
    @TableField(exist = false)
    private Integer occupationTime;
    /**
     * 抽取专家人数
     */
    @ApiModelProperty("抽取专家人数")
    @Excel(name = "抽取专家人数")
    private String expertNumber;

    @ApiModelProperty("业主评委数量")
    @Excel(name = "业主评委数量")
    private Integer ownerNumber;
    /**
     * 删除标记，0正常    0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常    0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    /** 创建者 */
    @TableField()
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField()
    private Date createTime;

    /** 更新者 */
    @TableField()
    private String updateBy;

    @TableField(exist = false)
    private List<Yzpw> yzpw;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField()
    private Date updateTime;

    @TableField(exist = false)
    private  BusiTenderProject project;
    @TableField(exist = false)
    private List<BusiExtractExpertGroup> groups;
    @TableField(exist = false)
    private List<BusiExtractExpertEvade> evades;
    @TableField(exist = false)
    private List<BusiExtractExpertResult> results;
}
