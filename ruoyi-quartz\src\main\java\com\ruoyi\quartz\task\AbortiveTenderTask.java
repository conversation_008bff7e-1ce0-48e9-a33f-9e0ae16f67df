package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiBiddingRecord;
import com.ruoyi.busi.domain.BusiExtractExpertApply;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import com.ruoyi.busi.enums.AbortiveTypeEnum;
import com.ruoyi.busi.service.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Log4j2
@Component("abortiveTenderTask")
public class AbortiveTenderTask
{
    @Autowired
    private IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    private IBusiWinningBidderNoticeService winningBidderNoticeService;
    @Autowired
    private IBusiBiddingRecordService biddingRecordService;
    /**
     * 专家抽取
     */
    public void execute()
    {
        log.info("执行定时任务---流标判断");
            List<BusiTenderNotice> tenderNoticeList = iBusiTenderNoticeService.selectForBidOpening();
            log.info("待开标公告数量："+tenderNoticeList.size());
            for (BusiTenderNotice notice : tenderNoticeList) {

                try {
                List<BusiBiddingRecord> biddingRecords = biddingRecordService.selectByProject(notice.getProjectId());
                if (biddingRecords!=null && biddingRecords.size() < 3) {
                    log.info("流标公告："+notice.getNoticeName()+"   投标供应商数量："+biddingRecords.size());
                    BusiWinningBidderNotice busiWinningBidderNotice = new BusiWinningBidderNotice();
                    busiWinningBidderNotice.setProjectId(notice.getProjectId());
                    busiWinningBidderNotice.setAbortiveType(AbortiveTypeEnum.BINDDING.getCode());
                    winningBidderNoticeService.saveAbortiveTenderNotice(busiWinningBidderNotice);
                }
                }catch (Exception e){
                    e.printStackTrace();
                    log.info("执行定时任务失败---流标判断---"+e.getCause().getMessage());
                }
            }
        log.info("结束定时任务---流标判断");
    }
}
