package com.ruoyi.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.ruoyi.base.mapper.BaseTreeDataMapper;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;

/**
 * 多级数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BaseTreeDataServiceImpl extends ServiceImpl<BaseTreeDataMapper, BaseTreeData> implements IBaseTreeDataService {
    /**
     * 查询多级数据列表
     *
     * @param baseTreeData 多级数据
     * @return 多级数据
     */
    @Override
    public List<BaseTreeData> selectList(BaseTreeData baseTreeData) {
        QueryWrapper<BaseTreeData> baseTreeDataQueryWrapper = getBaseTreeDataQueryWrapper(baseTreeData);
        return list(baseTreeDataQueryWrapper);
    }

    private QueryWrapper<BaseTreeData> getBaseTreeDataQueryWrapper(BaseTreeData baseTreeData) {
        QueryWrapper<BaseTreeData> baseTreeDataQueryWrapper = new QueryWrapper<>();
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getPid()), "pid", baseTreeData.getPid());
        baseTreeDataQueryWrapper.in(ObjectUtil.isNotEmpty(baseTreeData.getParams().get("pids")), "pid", baseTreeData.getParams().get("pids"));
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getThirdId()), "third_id", baseTreeData.getThirdId());
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getThirdPid()), "third_pid", baseTreeData.getThirdPid());
        baseTreeDataQueryWrapper.like(ObjectUtil.isNotEmpty(baseTreeData.getName()), "name", baseTreeData.getName());
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getCode()), "code", baseTreeData.getCode());
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getType()), "type", baseTreeData.getType());
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getIsEnabled()), "is_enabled", baseTreeData.getIsEnabled());
        baseTreeDataQueryWrapper.eq(ObjectUtil.isNotEmpty(baseTreeData.getSort()), "sort", baseTreeData.getSort());
        return baseTreeDataQueryWrapper;
    }

    @Cacheable(value = {"baseTreeData"}, key = "#root.methodName + ':' + #baseTreeData ", sync = true)
    @Override
    public List<BaseTreeData> selectListWithTree(BaseTreeData baseTreeData) {
        List<BaseTreeData> list = list(getBaseTreeDataQueryWrapper(baseTreeData));
        List<BaseTreeData> data = list.stream().filter(item -> {
            return item.getPid().equals(0L);
        }).map(item -> {
            item.setChildrens(getChildrens(item, list));
            return item;
        }).collect(Collectors.toList());
        return data;
    }

    private List<BaseTreeData> getChildrens(BaseTreeData root, List<BaseTreeData> all) {
        List<BaseTreeData> children = all.stream().filter(dataMultilevelEntity ->
                dataMultilevelEntity.getPid().equals(root.getId())
        ).map((dataMultilevelEntity) -> {
            dataMultilevelEntity.setChildrens(getChildrens(dataMultilevelEntity, all));
            return dataMultilevelEntity;
        }).sorted((menu1, menu2) -> {
            return (int) ((menu1.getSort() == null ? 0 : menu1.getSort()) - (menu2.getSort() == null ? 0 : menu2.getSort()));
        }).collect(Collectors.toList());

        return children;
    }
}
