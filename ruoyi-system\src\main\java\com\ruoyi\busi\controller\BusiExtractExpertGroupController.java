package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiExtractExpertGroup;
import com.ruoyi.busi.service.IBusiExtractExpertGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 专家组Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "专家组管理")
@RestController
@RequestMapping("/expert/group")
public class BusiExtractExpertGroupController extends BaseController {
    @Autowired
    private IBusiExtractExpertGroupService busiExtractExpertGroupService;

/**
 * 查询专家组列表
 */
@PreAuthorize("@ss.hasPermi('expert:group:list')")
@ApiOperation(value = "查询专家组列表")
@GetMapping("/list")
    public TableDataInfo list(BusiExtractExpertGroup busiExtractExpertGroup) {
        startPage();
        List<BusiExtractExpertGroup> list = busiExtractExpertGroupService.selectList(busiExtractExpertGroup);
        return getDataTable(list);
    }

    /**
     * 导出专家组列表
     */
    @PreAuthorize("@ss.hasPermi('expert:group:export')")
    @Log(title = "专家组", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家组列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExtractExpertGroup busiExtractExpertGroup) {
        List<BusiExtractExpertGroup> list = busiExtractExpertGroupService.selectList(busiExtractExpertGroup);
        ExcelUtil<BusiExtractExpertGroup> util = new ExcelUtil<BusiExtractExpertGroup>(BusiExtractExpertGroup. class);
        util.exportExcel(response, list, "专家组数据");
    }

    /**
     * 获取专家组详细信息
     */
    @PreAuthorize("@ss.hasPermi('expert:group:query')")
    @ApiOperation(value = "获取专家组详细信息")
    @ApiImplicitParam(name = "groupId", value = "专家组id", required = true, dataType = "Long")
    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId")Long groupId) {
        return success(busiExtractExpertGroupService.getById(groupId));
    }

    /**
     * 新增专家组
     */
    @PreAuthorize("@ss.hasPermi('expert:group:add')")
    @Log(title = "专家组", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家组")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExtractExpertGroup busiExtractExpertGroup) {
        busiExtractExpertGroup.setCreateBy(getUsername());
        busiExtractExpertGroup.setUpdateBy(getUsername());
        return toAjax(busiExtractExpertGroupService.save(busiExtractExpertGroup));
    }

    /**
     * 修改专家组
     */
    @PreAuthorize("@ss.hasPermi('expert:group:edit')")
    @Log(title = "专家组", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家组")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExtractExpertGroup busiExtractExpertGroup) {
        busiExtractExpertGroup.setUpdateBy(getUsername());
        return toAjax(busiExtractExpertGroupService.updateById(busiExtractExpertGroup));
    }

    /**
     * 删除专家组
     */
    @PreAuthorize("@ss.hasPermi('expert:group:remove')")
    @Log(title = "专家组", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家组")
    @DeleteMapping("/{groupIds}")
    public AjaxResult remove(@PathVariable Long[] groupIds) {
        return toAjax(busiExtractExpertGroupService.removeByIds(Arrays.asList(groupIds)));
    }
}
