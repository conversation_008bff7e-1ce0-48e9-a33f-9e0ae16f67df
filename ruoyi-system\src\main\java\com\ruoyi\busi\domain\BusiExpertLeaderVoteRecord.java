package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 专家组长投票记录对象 busi_expert_leader_vote_record
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@ApiModel("专家组长投票记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiExpertLeaderVoteRecordMapper.BusiExpertLeaderVoteRecordResult")
public class BusiExpertLeaderVoteRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 投票记录id（主键）
     */
    @ApiModelProperty("投票记录id（主键）")
    @TableId(type = IdType.ASSIGN_ID)
    private Long voteRecordId;
    @ApiModelProperty("抽取专家申请id")
    @Excel(name = "抽取专家申请id")
    private Long applyId;

    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 投票专家的id
     */
    @ApiModelProperty("投票专家的id")
    @Excel(name = "投票专家的id")
    private Long voterExpertId;
    /**
     * 被投票专家（候选组长）的id
     */
    @ApiModelProperty("被投票专家（候选组长）的id")
    @Excel(name = "被投票专家", readConverterExp = "候=选组长")
    private Long candidateExpertId;
    /**
     * 投票时间
     */
    @ApiModelProperty("投票时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "投票时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date voteTime;
    /**
     * 专家组ID，与专家抽取结果表中的group_id关联
     */
    @ApiModelProperty("专家组ID，与专家抽取结果表中的group_id关联")
    @Excel(name = "专家组ID，与专家抽取结果表中的group_id关联")
    private Long groupId;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
