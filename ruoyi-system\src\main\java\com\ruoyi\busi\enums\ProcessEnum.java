package com.ruoyi.busi.enums;

import lombok.Data;

public enum ProcessEnum {
    TENDER_INTENTION(0, "采购意向", "tender_intention_attachment"),
    TENDER_PROJECT(10, "采购项目", "tender_project_attachment"),
    TENDER_NOTICE(20, "采购公告", "busi_tender_notice_attachment"),
    EXTRACT_EXPERT(30, "专家抽取申请", "busi_tender_notice_attachment"),
    BIDDING_RECORD(999, "上传响应文件", "busi_bidding_record_attachment"),
    BID_OPENING(40, "开标情况", "bid_opening_attachment"),
    BID_EVALUATION(50, "评审情况", "busi_bid_evaluation_attachment"),
    WINNING_BIDDER_NOTICE(60, "成交结果公告", "busi_bidder_notice_attachment"),
    WINNING_BIDDER_ADVICE_NOTE(70, "成交通知书", "winning_bidder_advice_note_attachment"),
    TRANSACTION_CONTRACT(80, "合同", "winning_bidder_advice_note_attachment"),
    CANCEL_NOTICE(-1, "终止公告", "cancel_project_attachment"),
    PROCESS_INFO(200, "归档", "winning_bidder_advice_note_attachment");

    private Integer code;
    private String name;
    private String attachmentCode;

    ProcessEnum(Integer code, String name, String attachmentCode) {
        this.code = code;
        this.name = name;
        this.attachmentCode = attachmentCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getAttachmentCode() {
        return attachmentCode;
    }
}
