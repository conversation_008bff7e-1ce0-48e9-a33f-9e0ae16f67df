package com.ruoyi.busi.controller;

import java.io.IOException;
import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 专家抽取结果Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "专家抽取结果管理")
@RestController
@RequestMapping("/expert/result")
public class BusiExtractExpertResultController extends BaseController {
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;

/**
 * 查询专家抽取结果列表
 */
@PreAuthorize("@ss.hasPermi('expert:result:list')")
@ApiOperation(value = "查询专家抽取结果列表")
@GetMapping("/list")
    public TableDataInfo list(BusiExtractExpertResult busiExtractExpertResult) {
        startPage();
        List<BusiExtractExpertResult> list = busiExtractExpertResultService.selectList(busiExtractExpertResult);
        return getDataTable(list);
    }

    /**
     * 导出专家抽取结果列表
     */
    @PreAuthorize("@ss.hasPermi('expert:result:export')")
    @Log(title = "专家抽取结果", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出专家抽取结果列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExtractExpertResult busiExtractExpertResult) {
        List<BusiExtractExpertResult> list = busiExtractExpertResultService.selectList(busiExtractExpertResult);
        ExcelUtil<BusiExtractExpertResult> util = new ExcelUtil<BusiExtractExpertResult>(BusiExtractExpertResult. class);
        util.exportExcel(response, list, "专家抽取结果数据");
    }

    /**
     * 获取专家抽取结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('expert:result:query')")
    @ApiOperation(value = "获取专家抽取结果详细信息")
    @ApiImplicitParam(name = "resultId", value = "意向id", required = true, dataType = "Long")
    @GetMapping(value = "/{resultId}")
    public AjaxResult getInfo(@PathVariable("resultId")Long resultId) {
        return success(busiExtractExpertResultService.getById(resultId));
    }

    /**
     * 新增专家抽取结果
     */
    @PreAuthorize("@ss.hasPermi('expert:result:add')")
    @Log(title = "专家抽取结果", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增专家抽取结果")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExtractExpertResult busiExtractExpertResult) {
        busiExtractExpertResult.setCreateBy(getUsername());
        busiExtractExpertResult.setUpdateBy(getUsername());
        return toAjax(busiExtractExpertResultService.save(busiExtractExpertResult));
    }

    /**
     * 修改专家抽取结果
     */
    @PreAuthorize("@ss.hasPermi('expert:result:edit')")
    @Log(title = "专家抽取结果", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改专家抽取结果")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExtractExpertResult busiExtractExpertResult) {
        busiExtractExpertResult.setUpdateBy(getUsername());
        return toAjax(busiExtractExpertResultService.updateById(busiExtractExpertResult));
    }

    /**
     * 删除专家抽取结果
     */
    @PreAuthorize("@ss.hasPermi('expert:result:remove')")
    @Log(title = "专家抽取结果", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除专家抽取结果")
    @DeleteMapping("/{resultIds}")
    public AjaxResult remove(@PathVariable Long[] resultIds) {
        return toAjax(busiExtractExpertResultService.removeByIds(Arrays.asList(resultIds)));
    }


    /**
     * 评标专家登录
     */
    @ApiOperation(value = "评审-专家登录")
    @RequestMapping(value = "/login",method = RequestMethod.POST)
    public AjaxResult login(@RequestBody BusiExtractExpertResult busiExtractExpertResult) {

        return busiExtractExpertResultService.login(busiExtractExpertResult);
    }

    /**
     * 获取登录专家的所有项目
     */
    @ApiOperation(value = "获取登录专家的所有项目")
    @RequestMapping(value = "/getLoginUserProject",method = RequestMethod.POST)
    public AjaxResult getLoginUserProject(@RequestBody BusiExtractExpertResult busiExtractExpertResult) {
        return busiExtractExpertResultService.getLoginUserProject(busiExtractExpertResult);
    }



    /**
     * 评标专家登录
     */
    @ApiOperation(value = "获取项目的所有专家")
    @RequestMapping(value = "/getZhuanJiaByProjectId",method = RequestMethod.POST)
    public AjaxResult getZhuanJiaByProjectId(@RequestBody BusiExtractExpertResult busiExtractExpertResult) throws IOException {
        List<BusiExtractExpertResult> jiaByProjectId = busiExtractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
        if (jiaByProjectId == null || jiaByProjectId.isEmpty()) {
            return AjaxResult.error("没有抽取专家");
        }
        return AjaxResult.success(jiaByProjectId);
    }


}
