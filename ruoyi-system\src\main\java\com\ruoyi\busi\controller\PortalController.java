package com.ruoyi.busi.controller;

import com.ruoyi.busi.domain.vo.PortalDataVo;
import com.ruoyi.busi.service.IPortalService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "门户信息")
@RestController
@RequestMapping("/portal")
public class PortalController extends BaseController {
    @Autowired
    private IPortalService portalService;
    /**
     * 查询开标记录列表
     */
    @ApiOperation(value = "查询列表")
    @GetMapping("/list")
    public TableDataInfo list(PortalDataVo portalDataVo) {
        startPage();
        List<PortalDataVo> voList = portalService.list(portalDataVo);
        return  getDataTable(voList);
    }
    /**
     * 查询未开标记录列表
     */
    @ApiOperation(value = "查询未开标记录列表")
    @PostMapping("/notOpeninglist")
    public TableDataInfo notOpeninglist(@RequestBody PortalDataVo portalDataVo) {
        startPage();
        List<PortalDataVo> voList = portalService.notOpeninglist(portalDataVo);
        return  getDataTable(voList);
    }

    /**
     * 查询开标记录列表
     */
    @ApiOperation(value = "查询详情")
    @PostMapping("/info")
    public AjaxResult info(@RequestBody PortalDataVo portalDataVo) {
        return AjaxResult.success("成功",portalService.info(portalDataVo));
    }

    /**
     * 查询开标记录列表
     */
    @ApiOperation(value = "查询流程")
    @PostMapping("/process")
    public AjaxResult process(@RequestBody PortalDataVo portalDataVo) {
        return AjaxResult.success(portalService.process(portalDataVo));
    }
}
