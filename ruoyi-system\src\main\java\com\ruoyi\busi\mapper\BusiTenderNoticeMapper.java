package com.ruoyi.busi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.busi.domain.BusiTenderNotice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购公告信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Mapper
public interface BusiTenderNoticeMapper extends BaseMapper<BusiTenderNotice> {
    List<BusiTenderNotice> selectForBidOpening(@Param("tendererId") Long tendererId, @Param("agencyId") Long agencyId);

    BusiTenderNotice getOneIgnoreDeleted(@Param("info") BusiTenderNotice busiTenderNotice);
    List<BusiTenderNotice> getListIgnoreDeleted(@Param("info") BusiTenderNotice busiTenderNotice);
}