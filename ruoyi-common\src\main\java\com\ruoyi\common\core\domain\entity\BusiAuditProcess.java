package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 附件对象 busi_attachment
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("业务流程")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiAuditProcessMapper.BusiAuditProcessResult")
public class BusiAuditProcess extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务流程id
     */
    @ApiModelProperty("业务流程id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long auditProcessId;
    /**
     * 所属业务id
     */
    @ApiModelProperty("所属业务id")
    @Excel(name = "所属业务id")
    private Long busiId;
    /**
     * 所属业务id
     */
    @ApiModelProperty("所属业务类型")
    @Excel(name = "所属业务类型")
    private String busiType;

    /** 处理结果 */
    @ApiModelProperty("处理结果")
    @Excel(name = "处理结果")
    private Integer auditResult;
    /** 处理结果名称 */
    @ApiModelProperty("处理结果名称")
    @Excel(name = "处理结果名称")
    private String auditResultName;
    /** 处理意见 */
    @ApiModelProperty("处理结果")
    @Excel(name = "处理结果")
    private String auditRemark;

    /**
     * 业务状态 0未提交 1已提交待审核  2审核通过
     */
    @ApiModelProperty("业务状态")
    @Excel(name = "业务状态")
    private Integer busiState;

    /**
     * 所属业务id
     */
    @ApiModelProperty("操作人员")
    @Excel(name = "操作人员")
    private Long operator;
    /**
     * 所属业务id
     */
    @ApiModelProperty("操作人名称")
    @Excel(name = "操作人名称")
    private String operatorName;
    /**
     * 所属业务id
     */
    @ApiModelProperty("下一步操作人员")
    @Excel(name = "下一步操作人员")
    private Long nextOperator;


    /** 审核时间 */
    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
}
