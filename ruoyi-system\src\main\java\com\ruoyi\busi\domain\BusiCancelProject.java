package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BusiAuditProcessEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 取消采购项目对象 busi_cancel_project
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@ApiModel("取消采购项目对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiCancelProjectMapper.BusiCancelProjectResult")
public class BusiCancelProject  extends BusiAuditProcessEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 取消id
     */
    @ApiModelProperty("取消id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long cancelId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 取消原因
     */
    @ApiModelProperty("取消原因")
    @Excel(name = "取消原因")
    private String cancelReason;
    /**
     * 取消时间
     */
    @ApiModelProperty("取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cancelDate;
    //关闭状态(0审批中，1审批通过，2审批不通过)
    private Integer cancelStatus;

    private String cancelContent;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    @TableField(exist = false)
    private BusiTenderProject project;
    @TableField(exist = false)
    private List<Long> cancelIds;
    @TableField(exist =  false)
    private List<BusiAttachment> attachments = new ArrayList<>();
}
