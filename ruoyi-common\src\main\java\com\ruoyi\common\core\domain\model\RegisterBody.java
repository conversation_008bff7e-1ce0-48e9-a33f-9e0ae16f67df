package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import lombok.Data;

/**
 * 用户注册对象
 * 
 * <AUTHOR>
 */
@Data
public class RegisterBody extends LoginBody
{
    private Long userId;
    private String entName;
    private String entCode;
    private Integer entType;
    private String entAddress;
    private String entKeyAgencyAreas;
    private String type;
    private String opUser = "system";
    //营业执照
    private String businessLicense;
    //企业法人
    private String entLegalPerson;
    //企业法人联系方式
    private String entLegalPersonPhone;
    //法人身份证件 pdf地址正反面
    private String legalPersonCardFile;
    //企业联系人
     private String entLinkman;
     //企业联系人方式
     private String entContactPhone;
}
