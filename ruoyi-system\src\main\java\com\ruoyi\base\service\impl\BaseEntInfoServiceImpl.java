package com.ruoyi.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.mapper.BaseEntInfoMapper;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.enums.AuditBusiTypeEnum;
import com.ruoyi.busi.service.IBusiAuditProcessService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 企业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BaseEntInfoServiceImpl extends ServiceImpl<BaseEntInfoMapper, BaseEntInfo> implements IBaseEntInfoService {
    @Autowired
    private IBusiAuditProcessService auditProcessService;

    /**
     * 查询企业信息列表
     *
     * @param baseEntInfo 企业信息
     * @return 企业信息
     */
    @Override
    public List<BaseEntInfo> selectList(BaseEntInfo baseEntInfo) {
        QueryWrapper<BaseEntInfo> baseEntInfoQueryWrapper = new QueryWrapper<>();
        baseEntInfoQueryWrapper.like(ObjectUtil.isNotEmpty(baseEntInfo.getEntName()), "ent_name", baseEntInfo.getEntName());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntCode()), "ent_code", baseEntInfo.getEntCode());
        baseEntInfoQueryWrapper.like(ObjectUtil.isNotEmpty(baseEntInfo.getEntNature()), "ent_nature", baseEntInfo.getEntNature());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLinkman()), "ent_linkman", baseEntInfo.getEntLinkman());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntContactPhone()), "ent_contact_phone", baseEntInfo.getEntContactPhone());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLegalPerson()), "ent_legal_person", baseEntInfo.getEntLegalPerson());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLegalPersonPhone()), "ent_legal_person_phone", baseEntInfo.getEntLegalPersonPhone());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntStatus()), "ent_status", baseEntInfo.getEntStatus());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLogo()), "ent_logo", baseEntInfo.getEntLogo());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntType()), "ent_type", baseEntInfo.getEntType());
        baseEntInfoQueryWrapper.orderByDesc("create_time");
        return list(baseEntInfoQueryWrapper);
    }

    @Override
    public List<BaseEntInfo> getByIds(List<Long> entIds) {
        return list(new QueryWrapper<BaseEntInfo>().in("ent_id",entIds));
    }

    @Transactional
    @Override
    public AjaxResult updateAndAudit(BaseEntInfo baseEntInfo)  {
        try {
            BaseEntInfo bi = baseMapper.selectById(baseEntInfo.getEntId());
            if (baseEntInfo.getBusiState() == 0) {
                baseEntInfo.setBusiState(1);
                if (StringUtils.isNotEmpty(bi.getSecretKey())) {
                    String os = Md5Utils.hash(baseEntInfo.getOldSecretKey());
                    if(!bi.getSecretKey().equals(os)){
                        log.error("旧二级密码错误");
                        return AjaxResult.error("旧二级密码错误");
                    }
                }
                baseEntInfo.setSecretKey(Md5Utils.hash(baseEntInfo.getSecretKey()));
                auditProcessService.saveInfo(baseEntInfo.getEntId(), AuditBusiTypeEnum.ENT_INFO, 1, "提交", "提交", 1);
            } else if (baseEntInfo.getBusiState() == 1) {
                if (baseEntInfo.getAuditResult()) {
                    baseEntInfo.setBusiState(10);
                    baseEntInfo.setEntStatus(0);
                    auditProcessService.saveInfo(baseEntInfo.getEntId(), AuditBusiTypeEnum.ENT_INFO, 1, "通过", baseEntInfo.getAuditRemark(), 10);
                } else {
                    baseEntInfo.setBusiState(0);
                    auditProcessService.saveInfo(baseEntInfo.getEntId(), AuditBusiTypeEnum.ENT_INFO, 0, "退回", baseEntInfo.getAuditRemark(), 0);
                }
            }else{
                if (StringUtils.isNotEmpty(bi.getSecretKey())) {
                    String os = Md5Utils.hash(baseEntInfo.getOldSecretKey());
                    if(!bi.getSecretKey().equals(os)){
                        log.error("旧二级密码错误");
                        return AjaxResult.error("旧二级密码错误");
                    }
                }
                baseEntInfo.setSecretKey(Md5Utils.hash(baseEntInfo.getSecretKey()));
            }
            baseMapper.updateById(baseEntInfo);

            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    @Override
    public AjaxResult resetSecretKey(Long entId) {
        BaseEntInfo bi = baseMapper.selectById(entId);
        if(bi!=null){
            bi.setSecretKey(Md5Utils.hash("111111"));
            try {
                if (updateById(bi)) {
                    return AjaxResult.success("重置成功");
                }
                return AjaxResult.error("重置失败");
            } catch (Exception e) {
                e.printStackTrace();
                return AjaxResult.error("重置异常，"+e.getCause().getMessage());
            }
        }
        return AjaxResult.error("企业不存在");
    }

    public static void main(String[] args) {
        System.out.println(Md5Utils.hash("111111"));
    }
}
