package com.ruoyi.common.constant;

import java.util.*;

/**
 * xml字段映射
 */
public class XmlConstants {

 // 定义字段名与名称
 public static final Map<String, String> COLUMN_MAP_CODE2NAME;
 // 定义字段名与数据库列名的映射关系
 public static final Map<String, String> COLUMN_MAP_NAME2CODE;

 // 静态初始化块，用于初始化columnMap
 static {
  Map<String, String> code2Name = new HashMap<>();
  code2Name.put("projectName", "项目名称");
  code2Name.put("projectCode", "项目编号");
  code2Name.put("bidder", "投标人");
  code2Name.put("bidManager", "项目负责人");
  code2Name.put("bidderLegal", "法定代表人或其授权委托人");
  code2Name.put("bidPrice", "投标报价（元）");
  code2Name.put("qualityDemand", "质量要求");
  code2Name.put("warrantyPeriod", "保质期");
  code2Name.put("overTimeLimit", "供货期");
  code2Name.put("quantityListAndDrawings","工程量清单及图纸");
  code2Name.put("supplierName", "响应人名称");
  code2Name.put("procurementDemand","采购需求");
  code2Name.put("bidValidityPeriod","投标有效期");
  code2Name.put("warrantyPeriod", "质保期");

  // 使用Collections.unmodifiableMap确保map不可修改
  COLUMN_MAP_CODE2NAME = Collections.unmodifiableMap(code2Name);

  Map<String, String> name2Code = new HashMap<>();
  name2Code.put( "项目名称","projectName");
  name2Code.put("项目编号", "projectCode");
  name2Code.put("投标人", "bidder");
  name2Code.put("项目负责人", "bidManager");
  name2Code.put("法定代表人或其授权委托人", "bidderLegal");
  name2Code.put("投标报价（元）", "bidPrice");
  name2Code.put("质量要求", "qualityDemand");
  name2Code.put("保质期", "warrantyPeriod");
  name2Code.put("供货期", "overTimeLimit");
  name2Code.put("质保期", "warrantyPeriod");
  name2Code.put("工期", "overTimeLimit");
  name2Code.put("工程量清单及图纸","quantityListAndDrawings");
  name2Code.put("采购需求","procurementDemand");
  name2Code.put("投标有效期","bidValidityPeriod");
  name2Code.put("响应人名称","supplierName");

  // 使用Collections.unmodifiableMap确保map不可修改
  COLUMN_MAP_NAME2CODE = Collections.unmodifiableMap(name2Code);
 }



}
