package com.ruoyi.base.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 企业资质对象 base_ent_qualification
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("企业资质对象")
@TableName(resultMap = "com.ruoyi.base.mapper.BaseEntQualificationMapper.BaseEntQualificationResult")
public class BaseEntQualification extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 资质id
     */
    @ApiModelProperty("资质id")
        @TableId(type = IdType.ASSIGN_ID)
    private Long qualificationId;
    /**
     * 企业id
     */
    @ApiModelProperty("企业id")
            @Excel(name = "企业id")
    private Long entId;
    /**
     * 资质类型
     */
    @ApiModelProperty("资质类型")
            @Excel(name = "资质类型")
    private String qualificationType;
    /**
     * 资质文件
     */
    @ApiModelProperty("资质文件")
            @Excel(name = "资质文件")
    private String qualificationFile;
    /**
     * 有效期开始
     */
    @ApiModelProperty("有效期开始")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "有效期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date qualificationStartDate;
    /**
     * 有效期截至
     */
    @ApiModelProperty("有效期截至")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "有效期截至", width = 30, dateFormat = "yyyy-MM-dd")
    private Date qualificationEndDate;
    /**
     * 是否长期有效
     */
    @ApiModelProperty("是否长期有效")
            @Excel(name = "是否长期有效")
    private Integer isLongTerm;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

        }
