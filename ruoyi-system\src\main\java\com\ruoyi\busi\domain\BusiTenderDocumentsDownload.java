package com.ruoyi.busi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 采购文件下载记录对象 busi_tender_documents_download
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@ApiModel("采购文件下载记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiTenderDocumentsDownloadMapper.BusiTenderDocumentsDownloadResult")
public class BusiTenderDocumentsDownload extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 下载采购文件id
     */
    @ApiModelProperty("下载采购文件id")
        @TableId(type = IdType.ASSIGN_ID)
    private Long downloadId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
            @Excel(name = "项目id")
    private Long projectId;
    /**
     * 投标人id
     */
    @ApiModelProperty("投标人id")
            @Excel(name = "投标人id")
    private Long bidderId;
    /**
     * 投标人名称
     */
    @ApiModelProperty("投标人名称")
            @Excel(name = "投标人名称")
    private String bidderName;
    /**
     * 投标人代码
     */
    @ApiModelProperty("投标人代码")
            @Excel(name = "投标人代码")
    private String bidderCode;
    /**
     * 下载ip地址
     */
    @ApiModelProperty("下载ip地址")
            @Excel(name = "下载ip地址")
    private String downloadIp;
    /**
     * 采购文件版本
     */
    @ApiModelProperty("采购文件版本")
    @Excel(name = "采购文件版本")
    private Integer noticeVersion;
    /**
     * 下载时间
     */
    @ApiModelProperty("下载时间")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "下载时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date downloadTime;
    /**
     * 删除标记：0正常，1删除
     */
    @ApiModelProperty("删除标记：0正常，1删除")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private int delFlag;

    @TableField(exist = false)
    private BusiTenderProject project;
    @TableField(exist = false)
    private BusiTenderNotice notice;
}
