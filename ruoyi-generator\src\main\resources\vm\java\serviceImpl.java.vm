package ${packageName}.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
    #foreach ($column in $columns)
        #if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
        import com.ruoyi.common.utils.DateUtils;
            #break
        #end
    #end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
    #if($table.sub)
    import java.util.ArrayList;
    import com.ruoyi.common.utils.StringUtils;
    import org.springframework.transaction.annotation.Transactional;
    import ${packageName}.domain.${subClassName};
    #end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {
    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> selectList(${ClassName} ${className}) {
        QueryWrapper<${ClassName}> ${className}QueryWrapper = new QueryWrapper<>();
        #foreach($column in $columns)
            #set($queryType=$column.queryType)
            #set($javaField=$column.javaField)
            #set($javaType=$column.javaType)
            #set($columnName=$column.columnName)
            #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            #if($column.query)
                #if($column.queryType == "EQ")
                        ${className}QueryWrapper.eq(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "NE")
                        ${className}QueryWrapper.nq(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "GT")
                        ${className}QueryWrapper.gt(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "GTE")
                        ${className}QueryWrapper.ge(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "LT")
                        ${className}QueryWrapper.lt(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "LTE")
                        ${className}QueryWrapper.le(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "LIKE")
                        ${className}QueryWrapper.like(ObjectUtil.isNotEmpty(${className}.get$AttrName()),"$columnName",${className}.get$AttrName());
                #elseif($queryType == "BETWEEN")
                    String begin$AttrName = ${className}.getParams().get("begin$AttrName")!=null?${className}.getParams().get("begin$AttrName")+"":"";
                    String end$AttrName = ${className}.getParams().get("end$AttrName")+""!=null?${className}.getParams().get("end$AttrName")+"":"";
                        ${className}QueryWrapper.between(ObjectUtil.isNotEmpty(begin$AttrName) && ObjectUtil.isNotEmpty(end$AttrName), "$columnName", begin$AttrName , end$AttrName);
                #end
            #end
        #end
            ${className}QueryWrapper.apply(
                ObjectUtil.isNotEmpty(${className}.getParams().get("dataScope")),
        ${className}.getParams().get("dataScope")+""
        );
        return list(${className}QueryWrapper);
    }
}
