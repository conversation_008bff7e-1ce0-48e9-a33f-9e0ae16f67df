package com.ruoyi.common.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.domain.entity.BusiAuditProcess;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 审核Entity基类
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusiAuditProcessEntity extends BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 处理结果 */
    @TableField(exist = false)
    private Boolean auditResult;
    /** 处理意见 */
    @TableField(exist = false)
    private String auditRemark;

    /**
     * 业务状态 0未提交 1已提交待审核  10审核通过
     */
    private Integer busiState;

    /**
     *
     */
    @TableField(exist = false)
    private boolean submit;
    @TableField(exist = false)
    private boolean audit;

    /** 审核时间 */
    @TableField(exist = false)
    private Date auditTime;

    @TableField(exist = false)
    private BusiAuditProcess lastAuditProcess;

    @TableField(exist = false)
    private List<BusiAuditProcess> auditProcessList;


}
