//package com.ruoyi.common.exception;
//
//import com.ruoyi.common.core.domain.AjaxResult;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.dao.DuplicateKeyException;
//import org.springframework.validation.BindingResult;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
//@RestControllerAdvice(basePackages = "com.ruoyi.**.controller")
//public class ExceptionControllerAdvice {
//
//
//    @ExceptionHandler(DuplicateKeyException.class)
//    public AjaxResult handleDuplicateKeyException(DuplicateKeyException e){
//        log.error(e.getMessage(), e);
//        return AjaxResult.error("数据库中已存在该记录");
//    }
//
//    @ExceptionHandler(Exception.class)
//    public AjaxResult handleException(Exception e){
//        log.error(e.getMessage(), e);
//        return AjaxResult.error();
//    }
//
//    @ExceptionHandler(value = MethodArgumentNotValidException.class)
//    public AjaxResult handlerVaildException(MethodArgumentNotValidException e){
//        log.error("数据校验出现问题{},异常类型{}",e.getMessage(),e.getClass());
//
//        BindingResult bindingResult = e.getBindingResult();
//        Map<String,String> errorMsg = new HashMap<>();
//        bindingResult.getFieldErrors().forEach((fieldError)->{
//            errorMsg.put(fieldError.getField(),fieldError.getDefaultMessage());
//        });
//        return AjaxResult.error().put("data",errorMsg);
//    }
//
//    @ExceptionHandler(value = Throwable.class)
//    public AjaxResult handlerExecption(Throwable t){
//        log.error("错误",t);
//        return AjaxResult.error();
//    }
//}
