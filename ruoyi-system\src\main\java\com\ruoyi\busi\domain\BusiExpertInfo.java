package com.ruoyi.busi.domain;

import java.util.Arrays;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 专家信息对象 busi_expert_info
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@ApiModel("专家信息对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiExpertInfoMapper.BusiExpertInfoResult")
public class BusiExpertInfo extends BaseEntity implements Serializable
{
    private static final long serialVersionUID=1L;

    /**
     * 专家id
     */
    @ApiModelProperty("专家id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long expertId;
    /**
     * 性别，1男2女3未知
     */
    @ApiModelProperty("性别，1男2女3未知")
    @Excel(name = "性别，1男2女3未知")
    private Integer expertSex;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @Excel(name = "姓名")
    private String expertName;
    /**
     * 政治面貌
     */
    @ApiModelProperty("政治面貌")
    @Excel(name = "政治面貌")
    private String expertPoliticalOutlook;
    /**
     * 毕业学校
     */
    @ApiModelProperty("毕业学校")
    @Excel(name = "毕业学校")
    private String expertGraduationSchool;
    /**
     * 现从事专业
     */
    @ApiModelProperty("现从事专业")
    @Excel(name = "现从事专业")
    private String expertCareer;

    /**
     * 现任职务
     */
    @ApiModelProperty("现任职务")
    @Excel(name = "现任职务")
    private String expertDuty;
    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    @Excel(name = "手机号码")
    private String expertPhone;
    /**
     * 通信地址
     */
    @ApiModelProperty("通信地址")
    @Excel(name = "通信地址")
    private String expertMailAddress;
    /**
     * 工作年限
     */
    @ApiModelProperty("工作年限")
    @Excel(name = "工作年限")
    private Long expertWorkYears;
    /**
     * 证件类型
     */
    @ApiModelProperty("证件类型")
    @Excel(name = "证件类型")
    private String expertCertificateType;
    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expertBirthday;
    /**
     * 最高学历
     */
    @ApiModelProperty("最高学历")
    @Excel(name = "最高学历")
    private String expertDegree;
    /**
     * 毕业时间
     */
    @ApiModelProperty("毕业时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "毕业时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expertGraduactionTime;
    /**
     * 现工作单位
     */
    @ApiModelProperty("现工作单位")
    @Excel(name = "现工作单位")
    private String expertOrganization;
    /**
     * 职称/执业资格
     */
    @ApiModelProperty("职称/执业资格")
    @Excel(name = "职称/执业资格")
    private String expertTitles;
    /**
     * 电子邮箱
     */
    @ApiModelProperty("电子邮箱")
    @Excel(name = "电子邮箱")
    private String expertEmail;
    /**
     * 行业分类
     */
    @ApiModelProperty("行业分类")
    @Excel(name = "行业分类")
    private String expertIndustryClassify;
    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    @Excel(name = "证件号码")
    private String expertCertificateCode;
    /**
     * 民族
     */
    @ApiModelProperty("民族")
    @Excel(name = "民族")
    private String expertNation;
    /**
     * 照片
     */
    @ApiModelProperty("照片")
    @Excel(name = "照片")
    private String expertPhoto;
    /**
     * 所学专业
     */
    @ApiModelProperty("所学专业")
    @Excel(name = "所学专业")
    private String expertMajorStudy;
    /**
     * 常住地
     */
    @ApiModelProperty("常住地")
    @Excel(name = "常住地")
    private String expertPlace;
    /**
     * 职称等级，0中级1高级2其他
     */
    @ApiModelProperty("职称等级，0中级1高级2其他")
    @Excel(name = "职称等级，0中级1高级2其他")
    private Integer expertTitlesGrade;
    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    @Excel(name = "邮编")
    private String expertPostalCode;
    /**
     * 主评区域
     */
    @ApiModelProperty("主评区域")
    @Excel(name = "主评区域")
    private String expertMainEvaluatArea;
    /**
     * 评审品目
     */
    @ApiModelProperty("评审品目")
    @Excel(name = "评审品目")
    private String expertEvaluationItems;
    /**
     * 单位意见
     */
    @ApiModelProperty("单位意见")
    @Excel(name = "单位意见")
    private String expertOpinion;
    /**
     * 状态，0公示1启用
     */
    @ApiModelProperty("状态，0公示1启用")
    @Excel(name = "状态，0公示1启用")
    private Integer expertStatus;
    /**
     * 开户行
     */
    @ApiModelProperty("开户行")
    @Excel(name = "开户行")
    private String expertOpeningBank;
    /**
     * 户名
     */
    @ApiModelProperty("户名")
    @Excel(name = "户名")
    private String expertAccountName;
    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    @Excel(name = "银行账号")
    private String expertAccountCode;
    /**
     * 专家类别  字典表中维护
     */
    @ApiModelProperty("专家类别  字典表中维护")
    @Excel(name = "专家类别  字典表中维护")
    private Integer expertType;
    /**
     * 删除标记，0正常 1删除
     */
    @ApiModelProperty("删除标记，0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
