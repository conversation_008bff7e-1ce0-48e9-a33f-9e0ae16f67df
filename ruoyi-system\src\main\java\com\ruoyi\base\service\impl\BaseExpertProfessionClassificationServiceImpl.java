package com.ruoyi.base.service.impl;

import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.base.domain.BaseExpertProfessionClassification;
import com.ruoyi.base.domain.vo.ImportBaseExpertProfessionClassificationVo;
import com.ruoyi.base.mapper.BaseExpertProfessionClassificationMapper;
import com.ruoyi.base.service.IBaseExpertProfessionClassificationService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 专家专业分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
public class BaseExpertProfessionClassificationServiceImpl implements IBaseExpertProfessionClassificationService
{
    @Autowired
    private BaseExpertProfessionClassificationMapper baseExpertProfessionClassificationMapper;

    /**
     * 查询专家专业分类
     *
     * @param classificationCode 专家专业分类主键
     * @return 专家专业分类
     */
    @Override
    public BaseExpertProfessionClassification selectBaseExpertProfessionClassificationByClassificationCode(String classificationCode)
    {
        return baseExpertProfessionClassificationMapper.selectBaseExpertProfessionClassificationByClassificationCode(classificationCode);
    }

    /**
     * 查询专家专业分类列表
     *
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 专家专业分类
     */
    @Override
    public List<BaseExpertProfessionClassification> selectBaseExpertProfessionClassificationList(BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        return baseExpertProfessionClassificationMapper.selectBaseExpertProfessionClassificationList(baseExpertProfessionClassification);
    }

    /**
     * 新增专家专业分类
     *
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 结果
     */
    @Override
    public int insertBaseExpertProfessionClassification(BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        baseExpertProfessionClassification.setCreateTime(DateUtils.getNowDate());
        return baseExpertProfessionClassificationMapper.insertBaseExpertProfessionClassification(baseExpertProfessionClassification);
    }

    /**
     * 修改专家专业分类
     *
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 结果
     */
    @Override
    public int updateBaseExpertProfessionClassification(BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        baseExpertProfessionClassification.setUpdateTime(DateUtils.getNowDate());
        return baseExpertProfessionClassificationMapper.updateBaseExpertProfessionClassification(baseExpertProfessionClassification);
    }

    /**
     * 批量删除专家专业分类
     *
     * @param classificationCodes 需要删除的专家专业分类主键
     * @return 结果
     */
    @Override
    public int deleteBaseExpertProfessionClassificationByClassificationCodes(String[] classificationCodes)
    {
        return baseExpertProfessionClassificationMapper.deleteBaseExpertProfessionClassificationByClassificationCodes(classificationCodes);
    }

    /**
     * 删除专家专业分类信息
     *
     * @param classificationCode 专家专业分类主键
     * @return 结果
     */
    @Override
    public int deleteBaseExpertProfessionClassificationByClassificationCode(String classificationCode)
    {
        return baseExpertProfessionClassificationMapper.deleteBaseExpertProfessionClassificationByClassificationCode(classificationCode);
    }

    /**
     * 导入用户数据
     *
     * @param list 数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importClassification(List<BaseExpertProfessionClassification> list, Boolean isUpdateSupport, String operName) {
        QueryWrapper<BaseExpertProfessionClassification> baseExpertProfessionClassificationQueryWrapper = new QueryWrapper<>();
        baseExpertProfessionClassificationQueryWrapper.in("aa",111);
        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入专家专业分类数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        Long sort = 1L;
        Date date = new Date();
        for (BaseExpertProfessionClassification classification : list)
        {
            try{
                BaseExpertProfessionClassification classification1 = baseExpertProfessionClassificationMapper.selectBaseExpertProfessionClassificationByClassificationCode(classification.getClassificationCode());
                if(classification1 == null){
                    classification1.setClassificationCode(classification.getClassificationCode());
                    classification1.setClassificationLevel(1);
                    classification1.setClassificationName(classification.getClassificationName());
                    classification1.setClassificationSort(sort++);
                    classification1.setValidFlag(1);
                    classification1.setDelFlag(0);
                    classification1.setCreateTime(date);
                    classification1.setCreateBy(operName);
                    classification1.setUpdateTime(date);
                    classification1.setUpdateBy(operName);
                    baseExpertProfessionClassificationMapper.updateBaseExpertProfessionClassification(classification1);
                }else if(isUpdateSupport){
                    //修改
                    classification1.setClassificationCode(classification.getClassificationCode());
                    classification1.setClassificationLevel(1);
                    classification1.setClassificationName(classification.getClassificationName());
                    classification1.setClassificationSort(sort++);
                    classification1.setValidFlag(1);
                    classification1.setDelFlag(0);
                    classification1.setUpdateTime(date);
                    classification1.setUpdateBy(operName);
                    baseExpertProfessionClassificationMapper.updateBaseExpertProfessionClassification(classification1);
                }
                successNum++;
            }catch (Exception e){
                failureNum++;
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public String importCustomerClassification(List<ImportBaseExpertProfessionClassificationVo> list, boolean updateSupport, String operName) {

        if (StringUtils.isNull(list) || list.size() == 0)
        {
            throw new ServiceException("导入专家专业分类数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        Long sort = 1L;
        Date date = new Date();
        for (ImportBaseExpertProfessionClassificationVo classification : list)
        {
            //准备数据
            String zeroLevel = classification.getZeroLevel();
            String oneLevel = classification.getOneLevel();
            String twoLevel = classification.getTwoLevel();
            String threeLevel = classification.getThreeLevel();
            List<String> zero = new ArrayList<>();
            if(StringUtils.isNotEmpty(threeLevel)){
                zero = StringUtils.extractNumAndLetterParts(zeroLevel);
            }
            List<String> one = new ArrayList<>();
            if(StringUtils.isNotEmpty(threeLevel)){
                one = StringUtils.extractNumAndLetterParts(oneLevel);
            }
            List<String> two = new ArrayList<>();
            if(StringUtils.isNotEmpty(threeLevel)){
                two = StringUtils.extractNumAndLetterParts(twoLevel);
            }
            List<String> three = new ArrayList<>();
            if(StringUtils.isNotEmpty(threeLevel)){
                three = StringUtils.extractNumAndLetterParts(threeLevel);
            }

            try{
                if(zero.size()>0){
                    prodCustomerBaseExpertProfessionClassificationEntity(operName , updateSupport ,zero, sort, date,1);
                    sort++;
                }
                if(one.size()>0){
                    prodCustomerBaseExpertProfessionClassificationEntity(operName , updateSupport ,one, sort, date,2);
                    sort++;
                }
                if(two.size()>0){
                    prodCustomerBaseExpertProfessionClassificationEntity(operName , updateSupport , two, sort, date,3);
                    sort++;
                }
                if(three.size()>0){
                    prodCustomerBaseExpertProfessionClassificationEntity(operName, updateSupport,three, sort, date,4);
                    sort++;
                }
                successNum++;
            }catch (Exception e){
                failureNum++;
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    private void prodCustomerBaseExpertProfessionClassificationEntity(String operName , boolean updateSupport ,List<String> zero, Long sort, Date date,
            Integer level) {
        //查询数据库是否存在
        BaseExpertProfessionClassification zeroEntity = baseExpertProfessionClassificationMapper.selectBaseExpertProfessionClassificationByClassificationCode(zero.get(0));
        if(zeroEntity == null){
            //新增
            zeroEntity = new BaseExpertProfessionClassification();
            zeroEntity.setClassificationCode(zero.get(0));
            zeroEntity.setClassificationLevel(level);
            zeroEntity.setClassificationName(zero.get(1));
            zeroEntity.setClassificationSort(sort);
            zeroEntity.setValidFlag(1);
            zeroEntity.setDelFlag(0);
            zeroEntity.setCreateTime(date);
            zeroEntity.setCreateBy(operName);
            zeroEntity.setUpdateTime(date);
            zeroEntity.setUpdateBy(operName);
            baseExpertProfessionClassificationMapper.insertBaseExpertProfessionClassification(zeroEntity);
        }else if(updateSupport){
            //修改
            zeroEntity.setClassificationCode(zero.get(0));
            zeroEntity.setClassificationLevel(level);
            zeroEntity.setClassificationName(zero.get(1));
            zeroEntity.setClassificationSort(sort++);
            zeroEntity.setValidFlag(1);
            zeroEntity.setDelFlag(0);
            zeroEntity.setUpdateTime(date);
            zeroEntity.setUpdateBy(operName);
            baseExpertProfessionClassificationMapper.updateBaseExpertProfessionClassification(zeroEntity);
        }
    }
}
