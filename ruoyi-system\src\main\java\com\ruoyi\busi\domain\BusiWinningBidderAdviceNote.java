package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BusiAuditProcessEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 成交通知书对象 busi_winning_bidder_advice_note
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@ApiModel("成交通知书对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiWinningBidderAdviceNoteMapper.BusiWinningBidderAdviceNoteResult")
public class BusiWinningBidderAdviceNote extends BusiAuditProcessEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 通知书id
     */
    @ApiModelProperty("通知书id")
    @Excel(name = "通知书id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long noteId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 项目代码
     */
    @ApiModelProperty("项目代码")
    @Excel(name = "项目代码")
    private String projectCode;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 中标人id
     */
    @ApiModelProperty("中标人id")
    @Excel(name = "中标人id")
    private Long bidderId;
    /**
     * 中标人代码
     */
    @ApiModelProperty("中标人代码")
    @Excel(name = "中标人代码")
    private String bidderCode;
    /**
     * 中标人名称
     */
    @ApiModelProperty("中标人名称")
    @Excel(name = "中标人名称")
    private String bidderName;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


}
