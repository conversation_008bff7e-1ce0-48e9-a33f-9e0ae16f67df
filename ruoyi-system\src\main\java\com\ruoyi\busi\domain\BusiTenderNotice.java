package com.ruoyi.busi.domain;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.*;

/**
 * 采购公告信息对象 busi_tender_notice
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("采购公告信息对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiTenderNoticeMapper.BusiTenderNoticeResult")
public class BusiTenderNotice extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公告id
     */
    @ApiModelProperty("公告id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long noticeId;
    @ApiModelProperty("父id")
    private Long pId ;

    @ApiModelProperty("合同履约期限")
    private String contractPerformancePeriod ;
    @ApiModelProperty("付款方式")
    private String paymentMethod;


    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 公告代码
     */
    @ApiModelProperty("公告代码")
    @Excel(name = "公告代码")
    private String noticeCode;
    /**
     * 公告名称
     */
    @ApiModelProperty("公告名称")
    @Excel(name = "公告名称")
    private String noticeName;
    /**
     * 公告内容
     */
    @ApiModelProperty("公告内容")
    @Excel(name = "公告内容")
    private String noticeContent;
    /**
     * 公告类型 (1采购公告 2变更公告)
     */
    @ApiModelProperty("公告类型 (1采购公告 2变更公告)")
    @Excel(name = "公告类型 (1采购公告 2变更公告)")
    private Integer noticeType;
    /**
     * 公告开始时间
     */
    @ApiModelProperty("公告开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "公告开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date noticeStartTime;
    /**
     * 公告结束时间
     */
    @ApiModelProperty("公告结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "公告结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date noticeEndTime;
    /**
     * 开标时间
     */
    @ApiModelProperty("开标时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开标时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidOpeningTime;
    /**
     * 开标结束时间
     */
    @ApiModelProperty("开标结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开标结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidOpeningEndTime;
    /**
     * 评标时间
     */
    @ApiModelProperty("评标时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "评标时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidEvaluationTime;
    /**
     * 评标结束时间
     */
    @ApiModelProperty("评标结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "评标结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bidEvaluationEndTime;
    /**
     * 项目开始时间
     */
    @ApiModelProperty("项目开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectStartTime;
    /**
     * 项目期限 (天)
     */
    @ApiModelProperty("项目期限 (天)")
    @Excel(name = "项目期限 (天)")
    private Long projectDeadline;
    /**
     * 允许联合体投标 (0不允许 1允许)
     */
    @ApiModelProperty("允许联合体投标 (0不允许 1允许)")
    @Excel(name = "允许联合体投标 (0不允许 1允许)")
    private Integer allowCoalition;
    /**
     * 面向小微企业 (0不针对 1针对)
     */
    @ApiModelProperty("面向小微企业 (0不针对 1针对)")
    @Excel(name = "面向小微企业 (0不针对 1针对)")
    private Integer toSme;
    /**
     * 开标方式 (1线下开标 2线上开标)
     */
    @ApiModelProperty("开标方式 (1线下开标 2线上开标)")
    @Excel(name = "开标方式 (1线下开标 2线上开标)")
    private String bidOpeningMode;
    /**
     * 评标方式 (1线下评标 2线上评标)
     */
    @ApiModelProperty("评标方式 (1线下评标 2线上评标)")
    @Excel(name = "评标方式 (1线下评标 2线上评标)")
    private String bidEvaluationMode;
    /**
     * 删除标记 (0正常 1删除)
     */
    @ApiModelProperty("删除标记 (0正常 1删除)")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    /**
     * 公告状态0保存待发布 1已发布
     */
    @ApiModelProperty("公告状态0保存待发布 1已发布")
    @Excel(name = "公告状态0保存待发布 1已发布")
    private Integer noticeStats;
    /**
     * 公告变更次数
     */
    @ApiModelProperty("公告变更次数 ")
    @Excel(name = "公告变更次数 ")
    private Integer changeNum;
    /**
     * 报价单位
     */
    @ApiModelProperty("报价单位 ")
    @Excel(name = "报价单位 ")
    private String priceUnit;


    @ApiModelProperty("已选择的供应商信息 ")
    private String inviteBidder;


    @ApiModelProperty("首页用内容 ")
    @Excel(name = "首页用内容 ")
    private String mainContent;

    @ApiModelProperty("场地类型 ")
    @Excel(name = "场地类型（1平台  2自备） ")
    private Integer venueType;

    @TableField(exist = false)
    @ApiModelProperty("场地占用对象 ")
    private List<BusiVenueOccupy> busiVenueOccupys;

    @TableField(exist = false)
    @ApiModelProperty("操作类型（1是暂存，2是直接发布，3是单点发布）")
    private Integer operationType;

    @ApiModelProperty("采购文件获取开始时间 ")
    @Excel(name = "采购文件获取开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docAcquisitionStartTime;
    @ApiModelProperty("采购文件获取结束时间 ")
    @Excel(name = "采购文件获取结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docAcquisitionEndTime;
    @ApiModelProperty("响应文件提交截至时间 ")
    @Excel(name = "响应文件提交截至时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docResponseOverTime;
    @ApiModelProperty("是否允许分包 0否1是 ")
    @Excel(name = "是否允许分包 0否1是")
    private Integer subcontractingAllowed;
    @TableField(exist = false)
    @ApiModelProperty("已选择的供应商名称 ")
    private String inviteBidderName;

    @ApiModelProperty("并列第一名规则说明")
    @Excel(name = "并列第一名规则说明 ")
    private String sharedFirstRuleRemark;

    @ApiModelProperty("并列第一名规则 ")
    @Excel(name = "并列第一名规则（1系统随机 2根据特定条件选择")
    private Integer sharedFirstRule;
    /**
     * 项目
     */
    @TableField(exist = false)
    private BusiTenderProject project;
    @TableField(exist = false)
    private BusiVenueOccupy bidOpening;
    @TableField(exist = false)
    private BusiVenueOccupy bidEvaluation;
    @TableField(exist = false)
    private String firstNoticeName;
    @TableField(exist = false)
    private String noticeStatsName;
    @TableField(exist = false)
    private String allowCoalitionName;
    public String getAllowCoalitionName(){
        if(this.allowCoalition!=null) {
            switch (this.allowCoalition) {
                case 0:
                    return "不允许";
                case 1:
                    return "允许";
                default:
                    return "不允许";
            }
        }
        return "不允许";
    }
    public String getNoticeStatsName(){
        if(this.noticeStats!=null) {
            switch (this.noticeStats) {
                case -1:
                    return "待确认";
                case 0:
                    return "待发布";
                case 1:
                    return "已发布";
                default:
                    return "异常状态";
            }
        }
        return "";
    }


    /**
     * 附件列表
     */
    @TableField(exist = false)
    private String projectCode;
    @TableField(exist = false)
    private String projectName;
    @TableField(exist = false)
    private List<BusiAttachment> attachments = new ArrayList<>();

    public Long getId() {
        return this.noticeId;
    }

    public Integer getAnnouncementDuration(){
        if(getNoticeStartTime()!=null && getNoticeEndTime()!=null) {
            return DateUtils.differentDaysByMillisecond(getNoticeStartTime(), getNoticeEndTime());
        }
        return null;
    }

    public void initChangeInfo(){
        this.setPId(this.getNoticeId());
        this.setNoticeId(null);
//        this.setNoticeStartTime(null);
//        this.setNoticeEndTime(null);
//        this.setBidOpeningTime(null);
//        this.setBidOpeningEndTime(null);
//        this.setBidEvaluationTime(null);
//        this.setBidEvaluationEndTime(null);
        this.setChangeNum(this.getChangeNum()+1);
        this.setNoticeType(2);
        this.setNoticeName(this.getFirstNoticeName()+"第"+this.getChangeNum()+"次变更");
        this.setAttachments(new ArrayList<>());
        this.setAttachmentMap(new HashMap<>());
        this.setAttachmentNameMap(new HashMap<>());

    }
}
