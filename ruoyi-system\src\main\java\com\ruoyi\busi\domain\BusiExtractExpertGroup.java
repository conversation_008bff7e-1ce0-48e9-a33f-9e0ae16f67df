package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 专家组对象 busi_extract_expert_group
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("专家组对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiExtractExpertGroupMapper.BusiExtractExpertGroupResult")
public class BusiExtractExpertGroup extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 专家组id
     */
    @ApiModelProperty("专家组id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long groupId;
    /**
     * 专家抽取申请id
     */
    @ApiModelProperty("专家抽取申请id")
    @Excel(name = "专家抽取申请id")
    private Long applyId;
    /**
     * 专家组名称
     */
    @ApiModelProperty("专家组名称")
    @Excel(name = "专家组名称")
    private String groupName;
    /**
     * 抽取专家人数
     */
    @ApiModelProperty("抽取专家人数")
    @Excel(name = "抽取专家人数")
    private Integer expertNumber;
    /**
     * 专家专业
     */
    @ApiModelProperty("专家专业")
    @Excel(name = "专家专业")
    private String expertClassificationCode;
    /**
     * 专业类别
     */
    @ApiModelProperty("专业类别")
    @Excel(name = "专业类别")
    private Integer expertType;
    /**
     * 评审区域
     */
    @ApiModelProperty("评审区域")
    @Excel(name = "评审区域")
    private String groupAddress;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
