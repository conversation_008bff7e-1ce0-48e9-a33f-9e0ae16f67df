package com.ruoyi.base.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.base.mapper.BaseNationalEconomyIndustryMapper;
import com.ruoyi.base.domain.BaseNationalEconomyIndustry;
import com.ruoyi.base.service.IBaseNationalEconomyIndustryService;

/**
 * 国民经济行业分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
public class BaseNationalEconomyIndustryServiceImpl implements IBaseNationalEconomyIndustryService
{
    @Autowired
    private BaseNationalEconomyIndustryMapper baseNationalEconomyIndustryMapper;

    /**
     * 查询国民经济行业分类
     *
     * @param industrCode 国民经济行业分类主键
     * @return 国民经济行业分类
     */
    @Override
    public BaseNationalEconomyIndustry selectBaseNationalEconomyIndustryByIndustrCode(String industrCode)
    {
        return baseNationalEconomyIndustryMapper.selectBaseNationalEconomyIndustryByIndustrCode(industrCode);
    }

    /**
     * 查询国民经济行业分类列表
     *
     * @param baseNationalEconomyIndustry 国民经济行业分类
     * @return 国民经济行业分类
     */
    @Override
    public List<BaseNationalEconomyIndustry> selectBaseNationalEconomyIndustryList(BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        return baseNationalEconomyIndustryMapper.selectBaseNationalEconomyIndustryList(baseNationalEconomyIndustry);
    }

    /**
     * 新增国民经济行业分类
     *
     * @param baseNationalEconomyIndustry 国民经济行业分类
     * @return 结果
     */
    @Override
    public int insertBaseNationalEconomyIndustry(BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        baseNationalEconomyIndustry.setCreateTime(DateUtils.getNowDate());
        return baseNationalEconomyIndustryMapper.insertBaseNationalEconomyIndustry(baseNationalEconomyIndustry);
    }

    /**
     * 修改国民经济行业分类
     *
     * @param baseNationalEconomyIndustry 国民经济行业分类
     * @return 结果
     */
    @Override
    public int updateBaseNationalEconomyIndustry(BaseNationalEconomyIndustry baseNationalEconomyIndustry)
    {
        baseNationalEconomyIndustry.setUpdateTime(DateUtils.getNowDate());
        return baseNationalEconomyIndustryMapper.updateBaseNationalEconomyIndustry(baseNationalEconomyIndustry);
    }

    /**
     * 批量删除国民经济行业分类
     *
     * @param industrCodes 需要删除的国民经济行业分类主键
     * @return 结果
     */
    @Override
    public int deleteBaseNationalEconomyIndustryByIndustrCodes(String[] industrCodes)
    {
        return baseNationalEconomyIndustryMapper.deleteBaseNationalEconomyIndustryByIndustrCodes(industrCodes);
    }

    /**
     * 删除国民经济行业分类信息
     *
     * @param industrCode 国民经济行业分类主键
     * @return 结果
     */
    @Override
    public int deleteBaseNationalEconomyIndustryByIndustrCode(String industrCode)
    {
        return baseNationalEconomyIndustryMapper.deleteBaseNationalEconomyIndustryByIndustrCode(industrCode);
    }
}
