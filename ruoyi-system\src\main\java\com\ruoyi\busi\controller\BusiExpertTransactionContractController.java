package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiExpertTransactionContract;
import com.ruoyi.busi.service.IBusiExpertTransactionContractService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 成交合同Controller
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Api(tags = "成交合同管理")
@RestController
@RequestMapping("/transaction/contract")
public class BusiExpertTransactionContractController extends BaseController {
    @Autowired
    private IBusiExpertTransactionContractService busiExpertTransactionContractService;

/**
 * 查询成交合同列表
 */
@PreAuthorize("@ss.hasPermi('transaction:contract:list')")
@ApiOperation(value = "查询成交合同列表")
@GetMapping("/list")
    public TableDataInfo list(BusiExpertTransactionContract busiExpertTransactionContract) {
        startPage();
        List<BusiExpertTransactionContract> list = busiExpertTransactionContractService.selectList(busiExpertTransactionContract);
        return getDataTable(list);
    }

    /**
     * 导出成交合同列表
     */
    @PreAuthorize("@ss.hasPermi('transaction:contract:export')")
    @Log(title = "成交合同", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出成交合同列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiExpertTransactionContract busiExpertTransactionContract) {
        List<BusiExpertTransactionContract> list = busiExpertTransactionContractService.selectList(busiExpertTransactionContract);
        ExcelUtil<BusiExpertTransactionContract> util = new ExcelUtil<BusiExpertTransactionContract>(BusiExpertTransactionContract. class);
        util.exportExcel(response, list, "成交合同数据");
    }

    /**
     * 获取成交合同详细信息
     */
    @PreAuthorize("@ss.hasPermi('transaction:contract:query')")
    @ApiOperation(value = "获取成交合同详细信息")
    @ApiImplicitParam(name = "contractId", value = "合同ID", required = true, dataType = "Long")
    @GetMapping(value = "/{contractId}")
    public AjaxResult getInfo(@PathVariable("contractId")Long contractId) {
        return success(busiExpertTransactionContractService.getById(contractId));
    }

    /**
     * 新增成交合同
     */
    @PreAuthorize("@ss.hasPermi('transaction:contract:add')")
    @Log(title = "成交合同", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增成交合同")
    @PostMapping
    public AjaxResult add(@RequestBody BusiExpertTransactionContract busiExpertTransactionContract) {
        return busiExpertTransactionContractService.saveTransactionContract(busiExpertTransactionContract);
    }

    /**
     * 修改成交合同
     */
    @PreAuthorize("@ss.hasPermi('transaction:contract:edit')")
    @Log(title = "成交合同", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改成交合同")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiExpertTransactionContract busiExpertTransactionContract) {
        return toAjax(busiExpertTransactionContractService.updateById(busiExpertTransactionContract));
    }

    /**
     * 删除成交合同
     */
    @PreAuthorize("@ss.hasPermi('transaction:contract:remove')")
    @Log(title = "成交合同", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除成交合同")
    @DeleteMapping("/{contractIds}")
    public AjaxResult remove(@PathVariable Long[] contractIds) {
        return toAjax(busiExpertTransactionContractService.removeByIds(Arrays.asList(contractIds)));
    }
}
