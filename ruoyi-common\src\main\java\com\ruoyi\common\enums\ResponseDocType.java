package com.ruoyi.common.enums;

/**
 * 响应文件类型
 *
 * <AUTHOR>
 */
public enum ResponseDocType {

    CS_PROJECT_RES_DOC(101L, "磋商工程响应文件模板"),
    CS_GOODS_RES_DOC(102L, "磋商货物响应文件模板"),
    CS_SERVICE_RES_DOC(103L, "磋商服务响应文件模板"),
    XJ_GOODS_RES_DOC(104L, "询价货物响应文件模板");

    private final Long code;
    private final String info;

    ResponseDocType(Long code, String info) {
        this.code = code;
        this.info = info;
    }

    public Long getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
