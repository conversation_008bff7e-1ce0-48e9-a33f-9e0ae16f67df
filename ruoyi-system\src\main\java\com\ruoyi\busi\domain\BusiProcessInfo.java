package com.ruoyi.busi.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 全流程信息归档信息对象 busi_process_info
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@ApiModel("全流程信息归档信息对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiProcessInfoMapper.BusiProcessInfoResult")
public class BusiProcessInfo extends BaseEntity implements Serializable
        {
private static final long serialVersionUID=1L;

    /**
     * 归档id
     */
    @ApiModelProperty("归档id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long processId;
    /**
     * 业务状态
     */
    @ApiModelProperty("业务状态")
    @Excel(name = "业务状态")
    private Integer busiState;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;

    /**
     * 项目id
     */
    @ApiModelProperty("项目编号")
    @Excel(name = "项目编号")
    private String projectCode;

    /**
     * 项目id
     */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 删除标记 (0正常 1删除)
     */
    @ApiModelProperty("删除标记 (0正常 1删除)")
        @TableLogic(value = "0", delval = "1")
        @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private JSONArray attachments = new JSONArray();
}
