package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.FieldFill;

import java.io.Serializable;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 附件对象 busi_attachment
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("附件对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiAttachmentMapper.BusiAttachmentResult")
public class BusiAttachment extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附件id
     */
    @ApiModelProperty("附件id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long attachmentId;
    /**
     * 所属业务id
     */
    @ApiModelProperty("所属业务id")
    @Excel(name = "所属业务id")
    private Long busiId;
    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    @Excel(name = "附件名称")
    private String fileName;
    /**
     * 附件类型
     */
    @ApiModelProperty("附件类型")
    @Excel(name = "附件类型")
    private String fileType;
    /**
     * 附件后缀
     */
    @ApiModelProperty("附件后缀")
    @Excel(name = "附件后缀")
    private String fileSuffix;
    /**
     * 附件路径
     */
    @ApiModelProperty("附件路径")
    @Excel(name = "附件路径")
    private String filePath;
    /**
     * 附件md5码
     */
    @ApiModelProperty("附件md5码")
    @Excel(name = "附件md5码")
    private String fileMd5;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

}
