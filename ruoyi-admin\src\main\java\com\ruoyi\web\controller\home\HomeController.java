package com.ruoyi.web.controller.home;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.NoticeInfoAndIdsVo;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.controller.home.vo.DataStatisticsVo;
import com.ruoyi.web.controller.home.vo.TodoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Api(tags = "首页")
@RestController
@RequestMapping("/home")
public class HomeController extends BaseController {

    @Autowired
    IBusiWinningBidderNoticeService iBusiWinningBidderNoticeService;
    @Autowired
    IBusiWinningBidderAdviceNoteService winningBidderAdviceNoteService;
    @Autowired
    IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    IBusiTenderIntentionService iBusiTenderIntentionService;
    @Autowired
    IBusiBidderInfoService iBusiBidderInfoService;
    @Autowired
    IBaseEntInfoService iBaseEntInfoService;
    @Autowired
    IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    IBusiTenderDocumentsDownloadService iBusiTenderDocumentsDownloadService;
    @Autowired
    IBusiBiddingRecordService iBusiBiddingRecordService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IBusiCancelProjectService busiCancelProjectService;

    /**
     * 数据统计
     */
    @ApiOperation(value = "统计采购订单（中标结果）数据")
    @GetMapping("/purchase/statistics")
    public AjaxResult purchaseStatistics() {
        DataStatisticsVo dataStatisticsVo = new DataStatisticsVo();
        List<BusiTenderProject> busiTenderProjects = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>().and(w -> {
            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
        }));
        if (ObjectUtil.isNotEmpty(busiTenderProjects)) {
            List<Long> projectIds = new ArrayList<>();
            busiTenderProjects.forEach(item -> {
                projectIds.add(item.getProjectId());
            });
            //查询中标结果
            List<BusiWinningBidderNotice> winningBidderNoticeList = iBusiWinningBidderNoticeService.list(
                    new QueryWrapper<BusiWinningBidderNotice>().eq("notice_type", 1).in("project_id", projectIds));
            if (ObjectUtil.isNotEmpty(winningBidderNoticeList)) {
                List<Long> winProjectIds = new ArrayList<>();
                //累计订单个数统计所有中标结果
                dataStatisticsVo.setTotalNum(winningBidderNoticeList.size());
                final BigDecimal[] totalAmount = {BigDecimal.ZERO, BigDecimal.ZERO};
                winningBidderNoticeList.forEach(item -> {
                    if (null==item.getBidAmount()){
                        item.setBidAmount(BigDecimal.ZERO);
                    }
                    totalAmount[0] = totalAmount[0].add(item.getBidAmount());
                    winProjectIds.add(item.getProjectId());
                });
                //累计订单金额统计所有中标结果金额
                dataStatisticsVo.setTotalAmount(totalAmount[0]);
                //累计节约金额从发布公告金额减去中标结果金额
                busiTenderProjects.forEach(item -> {
                    if(winProjectIds.contains(item.getProjectId())) {
                        totalAmount[1] = totalAmount[1].add(item.getBudgetAmount());
                    }
                });
                BigDecimal economizeAmount = totalAmount[1].subtract(totalAmount[0]);
                dataStatisticsVo.setEconomizeAmount(economizeAmount);
            }
        }
        //累计意向数量，统计意向个数
        int count = iBusiTenderIntentionService.count(new QueryWrapper<BusiTenderIntention>().eq("tenderer_id", getEntId()));
        dataStatisticsVo.setIntentionNum(count);
        //加了projectNum，前端无法显示，所以还用intentionNum
        int projectCount = iBusiTenderProjectService.count(new QueryWrapper<BusiTenderProject>().eq("tenderer_id", getEntId()));
        dataStatisticsVo.setProjectNum(projectCount);

        return AjaxResult.success(dataStatisticsVo);
    }

    /**
     * 待办事项
     */
    @ApiOperation(value = "待办事项")
    @GetMapping("/purchase/todoList")
    public AjaxResult purchaseTodoList() {
        Date date = new Date();
        //获取企业信息  判断身份
        List<TodoVo> todoVos = new ArrayList<>();
        //查询所有项目
        List<BusiTenderProject> busiTenderProjects = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().and(w -> {
                    w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                }).eq("busi_state", 0)
        );
        busiTenderProjects.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待提交的采购项目");
            todoVo.setDescription("待提交的采购项目");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(2);
            todoVo.setType(5);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        List<SysRole> roles = loginUser.getUser().getRoles();
//        if(roles!=null && !roles.isEmpty() && roles.get(0).getRoleKey().equals("purchaser")) {
//            List<Long> intentionIds = busiTenderProjects.stream().map(item -> item.getProjectIntentionId()).collect(Collectors.toList());
//            //采购人
//            //用户可在采购项目列表页点击新增，或在首页待办事项中点击待发布的采购项目，进入发布采购项目页面。  发布完采购意向
//            List<BusiTenderIntention> todoPublishProjectList = iBusiTenderIntentionService.list(
//                    new QueryWrapper<BusiTenderIntention>().notIn(ObjectUtil.isNotEmpty(intentionIds), "intention_id", intentionIds));
//            todoPublishProjectList.forEach(item -> {
//                TodoVo todoVo = new TodoVo();
//                todoVo.setTitle(item.getIntentionName() + "---待发布采购项目");
//                todoVo.setDescription("项目意向公示结束，等待发布采购项目");
//                todoVo.setDueDate(item.getIntentionEndTime());
//                todoVo.setPriority(0);
//                todoVo.setType(0);
//                todoVo.setData(item);
//                todoVos.add(todoVo);
//            });
//        }
        //用户可在采购公告列表页点击新增，或在首页待办事项中点击待发布的采购公告（发布完采购项目），进入发布采购公告页面。  发布完采购项目
        List<BusiTenderProject> todoSaveNoticeList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", 10)
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        }).notInSql("project_id", "select project_id from busi_tender_notice where del_flag=0")
        );
        todoSaveNoticeList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待发布采购公告");
            todoVo.setDescription("采购项目已发布，等待发布采购公告");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(10);
            todoVo.setType(10);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

        //用户可在采购公告列表页点击新增，或在首页待办事项中点击待发布的采购公告（发布完采购项目），进入发布采购公告页面。  发布完采购项目
        List<BusiTenderProject> todoPublishNoticeList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", 10)
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        }).inSql("project_id", "select project_id from busi_tender_notice where notice_stats<1 and del_flag=0")
        );
        todoPublishNoticeList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待提交采购公告");
            todoVo.setDescription("采购公告已保存，等待提交采购公告");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(15);
            todoVo.setType(15);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

//        //用户可在专家抽取列表页点击新增，或在首页待办事项中点击待申请的专家抽取（发布完采购公告），进入专家抽取申请页面。  发布完采购公示公告
        List<BusiTenderProject> todoExtractExpertProjectList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.TENDER_NOTICE.getCode())
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        })
        );
//        //收集projectIds 查询过期公告
        List<Long> projectIds20 = todoExtractExpertProjectList.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(projectIds20)) {
//            noticeQuery.getParams().put("leNoticeEndTime",date);
            List<BusiTenderNotice> todoExtractExpertList = iBusiTenderNoticeService.list(
                    new QueryWrapper<BusiTenderNotice>().in("project_id", projectIds20).eq("DATE_FORMAT(bid_opening_time, '%Y-%m-%d')", DateUtils.getDate())
            );
            todoExtractExpertList.forEach(item -> {
                TodoVo todoVo = new TodoVo();
                todoVo.setTitle(item.getNoticeName() + "---待抽取评审专家");
                todoVo.setDescription("采购发布公示结束，等待抽取专家");
                todoVo.setDueDate(item.getCreateTime());
                todoVo.setPriority(20);
                todoVo.setType(20);
                todoVo.setData(item);
                todoVos.add(todoVo);
            });
        }

        // 开标时间到后，提醒待处理的开标情况
        List<BusiTenderProject> todoBidOpeningList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().between("project_status", ProcessEnum.TENDER_NOTICE.getCode(), ProcessEnum.EXTRACT_EXPERT.getCode())
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        }).inSql("project_id", "select project_id from busi_tender_notice where bid_opening_time < NOW()")
        );
        todoBidOpeningList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待发布开标情况");
            todoVo.setDescription("开标结束，等发布开标情况");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(30);
            todoVo.setType(30);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

//        //开标情况完成后，提醒待处理的评审情况
        List<BusiTenderProject> todoBidEvaluationList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.BID_OPENING.getCode())
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        })
        );
        todoBidEvaluationList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待发布评审情况");
            todoVo.setDescription("开标情况结束，等发布评审情况");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(40);
            todoVo.setType(40);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

//        //用户可在结果公告信息列表页点击新增，或在待办事项中点击待发布的结果公告，进入结果公告页面  评审结束后的项目
        List<BusiTenderProject> todoPublishResultList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.BID_EVALUATION.getCode())
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        })
        );
        todoPublishResultList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待发布结果公告");
            todoVo.setDescription("评审情况结束，等发布结果公告");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(50);
            todoVo.setType(50);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });
        //用户可在成交通知书信息列表页点击新增，或在待办事项中点击待生成的成交通知书，进入成交通知书页面。 发布完成交结果公告
//        List<BusiTenderProject> todoPublishWinningNoticeList = iBusiTenderProjectService.list(
//                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.WINNING_BIDDER_ADVICE_NOTE.getCode())
//                        .and(w -> {
//                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
//                        })
//        );
//        todoPublishWinningNoticeList.forEach(item -> {
//            TodoVo todoVo = new TodoVo();
//            todoVo.setTitle(item.getProjectName() + "---待成交通知书");
//            todoVo.setDescription("结果公告结束，等发布成交通知书");
//            todoVo.setDueDate(item.getCreateTime());
//            todoVo.setPriority(40);
//            todoVo.setType(40);
//            todoVo.setData(item);
//            todoVos.add(todoVo);
//        });

//        //待上传合同
        List<BusiTenderProject> todoContractList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.WINNING_BIDDER_ADVICE_NOTE.getCode())
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        }).inSql("project_id", "select project_id from busi_winning_bidder_advice_note where busi_state=10")
        );
        todoContractList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待发布采购合同");
            todoVo.setDescription("中标通知书结束，待发布采购合同");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(50);
            todoVo.setType(ProcessEnum.WINNING_BIDDER_ADVICE_NOTE.getCode());
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

//        //待处理归档
        List<BusiTenderProject> todoProcessList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.TRANSACTION_CONTRACT.getCode())
                        .and(w -> {
                            w.eq("tenderer_id", getEntId()).or().eq("agency_id", getEntId());
                        })
        );
        todoProcessList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待处理的归档");
            todoVo.setDescription("合同结束，待处理归档");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(50);
            todoVo.setType(ProcessEnum.TRANSACTION_CONTRACT.getCode());
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

        return AjaxResult.success(todoVos);
    }


    /**
     * 数据统计
     */
    @ApiOperation(value = "统计中标订单（中标结果）数据")
    @GetMapping("/supplier/statistics")
    public AjaxResult winBidStatistics() {
        DataStatisticsVo dataStatisticsVo = new DataStatisticsVo();
        List<BusiWinningBidderNotice> winningBidderNoticeList = iBusiWinningBidderNoticeService.list(
                new QueryWrapper<BusiWinningBidderNotice>().eq("bidder_id", getEntId())
                        .eq("notice_type", 1));
        final BigDecimal[] totalAmount = {BigDecimal.ZERO};
        winningBidderNoticeList.forEach(item -> {
            totalAmount[0] = totalAmount[0].add(item.getBidAmount());
        });
        int bidNum = iBusiBidderInfoService.count(new QueryWrapper<BusiBidderInfo>().eq("bidder_id", getEntId()));
        dataStatisticsVo.setTotalAmount(totalAmount[0]);
        dataStatisticsVo.setTotalNum(winningBidderNoticeList.size());
        dataStatisticsVo.setRegisterNum(winningBidderNoticeList.size());
        dataStatisticsVo.setBidNum(bidNum);
        return AjaxResult.success(dataStatisticsVo);
    }

    /**
     * 待办事项
     */
    @ApiOperation(value = "待办事项")
    @GetMapping("/supplier/todoList")
    public AjaxResult todoList() {
        //查询为开标项目
        List<BusiTenderProject> todoBidOpeningList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().between("project_status", 20, 40)
        );
        List<Long> projectIds = todoBidOpeningList.stream().map(item -> {
            return item.getProjectId();
        }).collect(Collectors.toList());
        Map<Long, BusiTenderProject> map = todoBidOpeningList.stream()
                .collect(Collectors.toMap(
                        BusiTenderProject::getProjectId,
                        result -> result,
                        (existing, replacement) -> existing)); // 在键冲突时保留现有的元素
        //排除已经报名的
        List<BusiBiddingRecord> records = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                .eq("bidder_id", getEntId()));
        List<Long> recordsProjectIds = records.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
        List<TodoVo> todoVos = new ArrayList<>();
        if(ObjectUtil.isEmpty(projectIds)){
            return AjaxResult.success(todoVos);
        }

        List<BusiTenderDocumentsDownload> downloads = iBusiTenderDocumentsDownloadService.list(new QueryWrapper<BusiTenderDocumentsDownload>()
                .eq("bidder_id", getEntId()).in(ObjectUtil.isNotEmpty(projectIds),"project_id", projectIds).notIn(ObjectUtil.isNotEmpty(recordsProjectIds), "project_id", recordsProjectIds));

        Set<Long> sb = new HashSet<>();
        downloads.stream().map(item -> {
            if(!sb.contains(item.getProjectId())) {
                TodoVo todoVo = new TodoVo();
                todoVo.setTitle(map.get(item.getProjectId()).getProjectName() + "---待上传投标文件");
                todoVo.setDescription("等待上传投标文件");
                todoVo.setDueDate(item.getDownloadTime());
                todoVo.setPriority(0);
                todoVo.setType(ProcessEnum.BIDDING_RECORD.getCode());
                todoVo.setData(item);
                todoVos.add(todoVo);
                sb.add(item.getProjectId());
            }
            return null;
        }).collect(Collectors.toList());

        //用户可在成交通知书信息列表页点击查看，或在待办事项中点击待确认的成交通知书，进入成交通知书页面。 发布完成交结果公告
        List<BusiWinningBidderAdviceNote> winningBidderAdviceNoteList = winningBidderAdviceNoteService.list(
                new QueryWrapper<BusiWinningBidderAdviceNote>().eq("bidder_id", getEntId()).eq("busi_state", 0).eq("del_flag", 0));

        winningBidderAdviceNoteList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待确认通知书");
            todoVo.setDescription("待确认成交通知书");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(70);
            todoVo.setType(ProcessEnum.WINNING_BIDDER_ADVICE_NOTE.getCode());
            todoVo.setData(item);
            todoVos.add(todoVo);
        });

//        //待上传合同
        List<BusiTenderProject> todoContractList = iBusiTenderProjectService.list(
                new QueryWrapper<BusiTenderProject>().eq("project_status", ProcessEnum.WINNING_BIDDER_ADVICE_NOTE.getCode())
                        .inSql("project_id", "select project_id from busi_winning_bidder_advice_note where busi_state=10 and bidder_id="+getEntId())
        );
        todoContractList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getProjectName() + "---待发布采购合同");
            todoVo.setDescription("中标通知书结束，待发布采购合同");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(50);
            todoVo.setType(ProcessEnum.TRANSACTION_CONTRACT.getCode());
            todoVo.setData(item);
            todoVos.add(todoVo);
        });
        return AjaxResult.success(todoVos);
    }


    /**
     * 已报名
     */
    @ApiOperation(value = "已报名")
    @GetMapping("/supplier/downLoadProject")
    public AjaxResult downLoadProject() {
        //查询下载过的
        //查询响应集合
        List<BusiBiddingRecord> records = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>()
                .eq("bidder_id", getEntId()));
        List<Long> projectIds = records.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
        //查询已开标项目
        List<BusiTenderNotice> notices = iBusiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>()
                .in("project_id", projectIds).le("bid_opening_time", new Date())
                .eq("notice_stats", 1));
        List<Long> exProjectIds = notices.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
        projectIds.addAll(exProjectIds);
        //查询下载集合（排除响应集合）（排除已开标项目）
        List<BusiTenderDocumentsDownload> downloads = iBusiTenderDocumentsDownloadService.list(new QueryWrapper<BusiTenderDocumentsDownload>()
                .notIn("project_id", projectIds).eq("bidder_id", getEntId()));
        //插入项目信息
        List<Long> selectProjectIds = downloads.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
        ProjectInfoAndIdsVo projectsByProjectIds = iBusiTenderProjectService.getProjectsByProjectIds(selectProjectIds);
        downloads.forEach(item -> {
            item.setProject(projectsByProjectIds.getProjectsMap().get(item.getProjectId()));
        });
        NoticeInfoAndIdsVo tenderNotisByProjectId = iBusiTenderNoticeService.getTenderNotisByProjectId(selectProjectIds);
        downloads.forEach(item -> {
            item.setNotice(tenderNotisByProjectId.getNoticesMap().get(item.getProjectId()));
        });
        return AjaxResult.success(downloads);
    }

    /**
     * 查询采购文件下载记录列表
     */
    @ApiOperation(value = "首页采购人我的项目")
    @GetMapping("/purchase/myProject")
    public TableDataInfo myProject(BusiTenderProject project) {
        startPage();
        BusiTenderProject projectQuery = new BusiTenderProject();
        List<BusiTenderProject> list = iBusiTenderProjectService.selectList(projectQuery);
        if (!list.isEmpty()){
            for (BusiTenderProject tmpProject : list) {
                Date endTDate = new Date();
                if (tmpProject.getProjectStatus() >0 && tmpProject.getProjectStatus() < 40) {
                    BusiTenderNotice notice = busiTenderNoticeService.selectByProject(tmpProject.getProjectId());
                    if (notice != null) {
                        tmpProject.setBidOpeningTime(notice.getBidOpeningTime());
                    }
                }
                if (tmpProject.getProjectStatus() < 0 || tmpProject.getProjectStatus() >= 80) {
                    endTDate = tmpProject.getUpdateTime();
                }
                long bd = Math.abs(endTDate.getTime() - tmpProject.getCreateTime().getTime());
                long days = TimeUnit.DAYS.convert(bd, TimeUnit.MILLISECONDS);
                tmpProject.setLastDay(Integer.parseInt(days+""));

            }
            return getDataTable(list);
        }

        return null;
    }


    /**
     * 数据统计
     */
    @ApiOperation(value = "统计采购订单（中标结果）数据")
    @GetMapping("/admin/statistics")
    public AjaxResult adminStatistics() {
        DataStatisticsVo dataStatisticsVo = new DataStatisticsVo();
        List<BusiTenderProject> busiTenderProjects = iBusiTenderProjectService.list(new QueryWrapper<BusiTenderProject>());
        if (ObjectUtil.isNotEmpty(busiTenderProjects)) {
            List<Long> projectIds = new ArrayList<>();
            busiTenderProjects.forEach(item -> {
                projectIds.add(item.getProjectId());
            });
            //查询中标结果
            List<BusiWinningBidderNotice> winningBidderNoticeList = iBusiWinningBidderNoticeService.list(
                    new QueryWrapper<BusiWinningBidderNotice>().eq("notice_type", 1).in("project_id", projectIds));
            if (ObjectUtil.isNotEmpty(winningBidderNoticeList)) {
                List<Long> winProjectIds = new ArrayList<>();
                //累计订单个数统计所有中标结果
                dataStatisticsVo.setTotalNum(winningBidderNoticeList.size());
                final BigDecimal[] totalAmount = {BigDecimal.ZERO, BigDecimal.ZERO};
                winningBidderNoticeList.forEach(item -> {
                    totalAmount[0] = totalAmount[0].add(item.getBidAmount());
                    winProjectIds.add(item.getProjectId());
                });
                //累计订单金额统计所有中标结果金额
                dataStatisticsVo.setTotalAmount(totalAmount[0]);
                //累计节约金额从发布公告金额减去中标结果金额
                busiTenderProjects.forEach(item -> {
                    if(winProjectIds.contains(item.getProjectId())) {
                        totalAmount[1] = totalAmount[1].add(item.getBudgetAmount());
                    }
                });
                BigDecimal economizeAmount = totalAmount[1].subtract(totalAmount[0]);
                dataStatisticsVo.setEconomizeAmount(economizeAmount);
            }
        }
        //累计意向数量，统计意向个数
        int count = iBusiTenderIntentionService.count(new QueryWrapper<BusiTenderIntention>());
        dataStatisticsVo.setIntentionNum(count);
        return AjaxResult.success(dataStatisticsVo);
    }

    /**
     * 待办事项
     */
    @ApiOperation(value = "待办事项")
    @GetMapping("/admin/todoList")
    public AjaxResult adminTodoList() {
        Date date = new Date();
        //获取企业信息  判断身份
        List<TodoVo> todoVos = new ArrayList<>();

        //用户可在采购公告列表页点击新增，或在首页待办事项中点击待发布的采购公告（发布完采购项目），进入发布采购公告页面。  发布完采购项目
        List<BaseEntInfo> todoEntInfoList = iBaseEntInfoService.list(
                new QueryWrapper<BaseEntInfo>().eq("ent_status", 1));
        todoEntInfoList.forEach(item -> {
            TodoVo todoVo = new TodoVo();
            todoVo.setTitle(item.getEntName() + "---待审核");
            todoVo.setDescription("待审核的新增企业");
            todoVo.setDueDate(item.getCreateTime());
            todoVo.setPriority(10);
            todoVo.setType(10);
            todoVo.setData(item);
            todoVos.add(todoVo);
        });
//        //用户可在专家抽取列表页点击新增，或在首页待办事项中点击待申请的专家抽取（发布完采购公告），进入专家抽取申请页面。  发布完采购公示公告
        List<BusiCancelProject> todoCancelProjectList = busiCancelProjectService.list(
                new QueryWrapper<BusiCancelProject>().eq("busi_state", 1));
            todoCancelProjectList.forEach(item -> {
                TodoVo todoVo = new TodoVo();
                todoVo.setTitle(item.getProjectName() + "---待审核的取消公告");
                todoVo.setDescription("待审核的取消公告");
                todoVo.setDueDate(item.getCreateTime());
                todoVo.setPriority(20);
                todoVo.setType(20);
                todoVo.setData(item);
                todoVos.add(todoVo);
            });

        return AjaxResult.success(todoVos);
    }
}
