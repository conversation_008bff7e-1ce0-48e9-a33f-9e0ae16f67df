package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 成交合同对象 busi_expert_transaction_contract
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Data
@ApiModel("成交合同对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiExpertTransactionContractMapper.BusiExpertTransactionContractResult")
public class BusiExpertTransactionContract extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 合同ID
     */
    @ApiModelProperty("合同ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long contractId;
    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @Excel(name = "项目ID")
    private Long projectId;
    /**
     * 合同签署时间
     */
    @ApiModelProperty("合同签署时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同签署时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date contractSigningDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;
    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<BusiAttachment> attachments;

    @TableField(exist = false)
    private BusiTenderProject project;

    @TableField(exist = false)
    private BusiBidderInfo busiBidderInfo;
}
