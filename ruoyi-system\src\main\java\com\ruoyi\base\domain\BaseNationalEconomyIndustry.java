package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 国民经济行业分类对象 base_national_economy_industry
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public class BaseNationalEconomyIndustry extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 行业代码分类 */
    @Excel(name = "行业代码分类")
    private String industrCode;

    /** 行业数据等级 */
    @Excel(name = "行业数据等级")
    private Long industrLevel;

    /** 行业名称 */
    @Excel(name = "行业名称")
    private String industryName;

    /** 行业排序 */
    @Excel(name = "行业排序")
    private Long industrySort;

    /** 有效标识0无效1有效 */
    @Excel(name = "有效标识0无效1有效")
    private Integer validFlag;

    /** 删除标识0正常1删除 */
    @TableLogic(value = "0" , delval = "1")
    private Integer delFlag;

    public void setIndustrCode(String industrCode) 
    {
        this.industrCode = industrCode;
    }

    public String getIndustrCode() 
    {
        return industrCode;
    }
    public void setIndustrLevel(Long industrLevel) 
    {
        this.industrLevel = industrLevel;
    }

    public Long getIndustrLevel() 
    {
        return industrLevel;
    }
    public void setIndustryName(String industryName) 
    {
        this.industryName = industryName;
    }

    public String getIndustryName() 
    {
        return industryName;
    }
    public void setIndustrySort(Long industrySort) 
    {
        this.industrySort = industrySort;
    }

    public Long getIndustrySort() 
    {
        return industrySort;
    }
    public void setValidFlag(Integer validFlag) 
    {
        this.validFlag = validFlag;
    }

    public Integer getValidFlag() 
    {
        return validFlag;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("industrCode", getIndustrCode())
            .append("industrLevel", getIndustrLevel())
            .append("industryName", getIndustryName())
            .append("industrySort", getIndustrySort())
            .append("validFlag", getValidFlag())
            .append("delFlag", getDelFlag())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
