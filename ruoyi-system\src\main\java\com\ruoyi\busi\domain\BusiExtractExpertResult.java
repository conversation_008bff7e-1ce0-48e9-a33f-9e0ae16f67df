package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.utils.ZhuanJiaInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 专家抽取结果对象 busi_extract_expert_result
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("专家抽取结果对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiExtractExpertResultMapper.BusiExtractExpertResultResult")
public class BusiExtractExpertResult extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 意向id
     */
    @ApiModelProperty("意向id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long resultId;
    /**
     * 申请id
     */
    @ApiModelProperty("申请id")
    @Excel(name = "申请id")
    private Long applyId;
    /**
     * 专家id
     */
    @ApiModelProperty("专家id")
    @Excel(name = "专家id")
    @JsonProperty("id")
    private Long expertId;
    /**
     * 专家姓名
     */
    @ApiModelProperty("专家姓名")
    @Excel(name = "专家姓名")
    @JsonProperty("xm")
    private String expertName;
    /**
     * 专家证件号
     */
    @ApiModelProperty("专家证件号")
    @Excel(name = "专家证件号")
    @JsonProperty("zjhm")
    private String expertCode;
    /**
     * 对专家评价
     */
    @ApiModelProperty("对专家评价")
    @Excel(name = "对专家评价")
    private String expertAppraise;

    @ApiModelProperty("签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    @ApiModelProperty("签到状态未签到0已签到1")
    private Integer signStatus;


    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建者
     */
    @TableField()
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField()
    private Date createTime;

    /**
     * 更新者
     */
    @TableField()
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField()
    private Date updateTime;

    private Long groupId;

    private Long thirtyId;
    private String company;
    private String phone;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private String searchDate;
    private Integer isOwner;
    //是否回避（0否1是）
    private Integer isAvoid;

    //是否是专家组长（0否1是）
    private Integer expertLeader;
    @TableField(exist = false)
    private Long projectId;
    @TableField(exist = false)
    private Integer expertLeaderCount;
    @TableField(exist = false)
    ZhuanJiaInfoVo.ExpertBean zhuanJiaInfoVo;

}
