package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiExtractExpertApply;
import com.ruoyi.busi.domain.BusiExtractExpertEvade;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiExtractExpertApplyService;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.core.domain.AjaxResult;
import lombok.extern.log4j.Log4j;
import org.apache.commons.math3.analysis.function.Exp;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("expertTask")
public class ExpertTask
{
    @Autowired
    private IBusiTenderNoticeService iBusiTenderNoticeService;
    @Autowired
    private IBusiExtractExpertApplyService iBusiExtractExpertApplyService;

    /**
     * 专家抽取
     */
    public void extract()
    {
        System.out.println("执行无参方法---专家抽取");
        try {
            //获取已提交申请的(随机抽取)项目ids
            List<BusiExtractExpertApply> list = iBusiExtractExpertApplyService.list(new QueryWrapper<BusiExtractExpertApply>()
                    .eq("apply_method", 0).eq("apply_status", 1).eq("apply_id","1127921347519493"));
            if (list.isEmpty()) {
               throw new RuntimeException("未查到待抽取的项目");//.eq("apply_id","1127886254629893")
            }
            //提取项目id
            List<Long> projectIds = list.stream()
                    .map(BusiExtractExpertApply::getProjectId)
                    .collect(Collectors.toList());

            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date();
            //根据开标时间查询公告
            List<BusiTenderNotice> noticeList = iBusiTenderNoticeService.list(new QueryWrapper<BusiTenderNotice>().eq("bid_opening_time", format.format(date)).in("project_id",projectIds));
            //从公告集合中获取项目集合
            List<Long> projectId = noticeList.stream().map(BusiTenderNotice::getProjectId).collect(Collectors.toList());
            //根据项目集合获取专家抽取申请
            List<BusiExtractExpertApply> applyList = iBusiExtractExpertApplyService.list(new QueryWrapper<BusiExtractExpertApply>().in("project_id",projectIds));
            //执行专家抽取申请
            for (BusiExtractExpertApply busiExtractExpertApply : applyList) {
                iBusiExtractExpertApplyService.suiJiChouQu(busiExtractExpertApply);
            }
        }catch (Exception e){
            System.out.println("执行无参方法---专家抽取---失败---Start");
            e.printStackTrace();
            System.out.println("执行无参方法---专家抽取---失败---End");
        }
        System.out.println("结束无参方法---专家抽取");
    }
}
