package com.ruoyi.web.controller.license;

/**
 * 用于获取客户端服务器信息
 */
//@RestController
//@RequestMapping("/client")
public class LicenseCreatorController {

//    /**
//     * 获取服务器硬件信息
//     */
//    @RequestMapping(value = "/getServerInfos",produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
//    public LicenseCheckModel getServerInfos(@RequestParam(value = "osName",required = false) String osName) {
//        //操作系统类型
//        if(StringUtils.isBlank(osName)){
//            osName = System.getProperty("os.name");
//        }
//        osName = osName.toLowerCase();
//
//        AbstractServerInfos abstractServerInfos = null;
//
//        //根据不同操作系统类型选择不同的数据获取方法
//        if (osName.startsWith("windows")) {
//            abstractServerInfos = new WindowsServerInfos();
//        } else if (osName.startsWith("linux")) {
//            abstractServerInfos = new LinuxServerInfos();
//        }else{//其他服务器类型
//            abstractServerInfos = new LinuxServerInfos();
//        }
//        return abstractServerInfos.getServerInfos();
//    }


}
