package com.ruoyi.busi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiTenderDocumentsDownload;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.service.IBusiTenderDocumentsDownloadService;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.uuid.IdUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 采购公告信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "采购公告信息管理")
@RestController
@RequestMapping("/tender/notice")
public class BusiTenderNoticeController extends BaseController {
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IBusiTenderDocumentsDownloadService busiTenderDocumentsDownloadService;

    /**
 * 查询采购公告信息列表
 */
@PreAuthorize("@ss.hasPermi('tender:notice:list')")
@ApiOperation(value = "查询采购公告信息列表")
@GetMapping("/list")
    public TableDataInfo list(BusiTenderNotice busiTenderNotice) {
        startPage();
        List<BusiTenderNotice> list = busiTenderNoticeService.selectList(busiTenderNotice);
        return getDataTable(list);
    }

    /**
     * 导出采购公告信息列表
     */
    @PreAuthorize("@ss.hasPermi('tender:notice:export')")
    @Log(title = "采购公告信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购公告信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiTenderNotice busiTenderNotice) {
        List<BusiTenderNotice> list = busiTenderNoticeService.selectList(busiTenderNotice);
        ExcelUtil<BusiTenderNotice> util = new ExcelUtil<BusiTenderNotice>(BusiTenderNotice. class);
        util.exportExcel(response, list, "采购公告信息数据");
    }

    /**
     * 获取采购公告信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('tender:notice:query')")
    @ApiOperation(value = "获取采购公告信息详细信息")
    @ApiImplicitParam(name = "noticeId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId")Long noticeId) {
        return busiTenderNoticeService.getTenderNoticeInfo(noticeId);
    }
    /**
     *发布采购公告信息
     */
    @ApiOperation(value = "发布采购公告信息详细信息")
    @PostMapping(value = "/{updateAnnouncementInfo}")
    public AjaxResult updateAnnouncementInfo(@RequestBody BusiTenderNotice busiTenderNotice) {
        return busiTenderNoticeService.updateAnnouncementInfo(busiTenderNotice);
    }

    /**
     * 获取采购公告信息详细信息
     */
    @ApiOperation(value = "获取采购公告信息详细信息")
    @GetMapping(value = "/getChangeNoticeInfo/{noticeId}/{type}")
    public AjaxResult getChangeNoticeInfo(@PathVariable("noticeId")Long noticeId, @PathVariable("type")Integer type) {
        return busiTenderNoticeService.getChangeNoticeInfo(noticeId, type);
    }

  /**
     *变更采购公告信息详细信息
     */
    @ApiOperation(value = "变更采购公告信息详细信息")
    @PostMapping(value = "/changeAnnouncementInfo")
    public AjaxResult changeAnnouncementInfo(@RequestBody BusiTenderNotice busiTenderNotice) throws Exception {
        return busiTenderNoticeService.changeAnnouncementInfo(busiTenderNotice);
    }


    /**
     * 新增采购公告信息
     */
    @PreAuthorize("@ss.hasPermi('tender:notice:add')")
    @Log(title = "采购公告信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购公告信息")
    @PostMapping
    public AjaxResult add(@RequestBody BusiTenderNotice busiTenderNotice) throws Exception {
        busiTenderNotice.setNoticeCode(IdUtils.generateCodeWithPrefix("ZBGG"));
        return busiTenderNoticeService.saveTenderNoticeAttachment(busiTenderNotice);
    }

    /**
     * 修改采购公告信息
     */
    @PreAuthorize("@ss.hasPermi('tender:notice:edit')")
    @Log(title = "采购公告信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购公告信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiTenderNotice busiTenderNotice) {
        busiTenderNotice.setUpdateBy(getUsername());
        return toAjax(busiTenderNoticeService.updateById(busiTenderNotice));
    }

    /**
     * 删除采购公告信息
     */
    @PreAuthorize("@ss.hasPermi('tender:notice:remove')")
    @Log(title = "采购公告信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购公告信息")
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        List<BusiTenderNotice> busiTenderNoticeList=busiTenderNoticeService.listByIds(Arrays.asList(noticeIds));
        for (BusiTenderNotice busiTenderNotice : busiTenderNoticeList) {
            busiTenderNotice.setUpdateBy(getUsername());
            busiTenderNotice.setUpdateTime(new Date());
        }
        return busiTenderNoticeService.removeTenderNotice(busiTenderNoticeList);
    }


    @GetMapping("/downLoadTenderNoticeFile")
    public AjaxResult downLoadFile(@RequestParam Map<String,Object> params, HttpServletRequest request, HttpServletResponse response) {
        String projectId = params.get("projectId").toString();
        String filePath = params.get("filePath").toString();
        BusiTenderDocumentsDownload busiTenderDocumentsDownload=new BusiTenderDocumentsDownload();
        busiTenderDocumentsDownload.setProjectId(Long.parseLong(projectId));
        LoginUser loginUser = getLoginUser();
        busiTenderDocumentsDownload.setBidderId(getEntId());
        busiTenderDocumentsDownload.setBidderName(loginUser.getUser().getEnt().getEntName());
        busiTenderDocumentsDownload.setDownloadIp(request.getRemoteAddr());
        busiTenderDocumentsDownload.setDownloadTime(new Date());
        return AjaxResult.success(busiTenderDocumentsDownloadService.save(busiTenderDocumentsDownload));
    }

    @ApiOperation(value = "供应商统计")
    @GetMapping("/getNoticeStatistics")
    public AjaxResult  getNoticeStatistics(){
        return busiTenderNoticeService.getNoticeStatistics(getLoginUser());
    }

    @ApiOperation(value = "供应商统计招投标信息")
    @GetMapping("/getNoticeStatisticsByType")
    public AjaxResult  getNoticeStatisticsByType(Integer type){
        return busiTenderNoticeService.getNoticeStatisticsByType(getLoginUser(),type);
    }

    @ApiOperation(value = "查询采购公告详细信息")
    @GetMapping("/noticeView")
    public AjaxResult noticeView(Long tenderNoticeId){
        BusiTenderVo vo = busiTenderNoticeService.getTenderNotice(tenderNoticeId, false);
        if(vo!=null){
            return AjaxResult.success(vo);
        }
        return AjaxResult.error();
    }

    @ApiOperation(value = "首页查询采购公告详细信息")
    @GetMapping("/getTenderNoticeForMain")
    public AjaxResult getTenderNotice(Long tenderNoticeId){
        BusiTenderVo vo = busiTenderNoticeService.getTenderNotice(tenderNoticeId, true);
        if(vo!=null){
            return AjaxResult.success(vo);
        }
        return AjaxResult.error();
    }

    @ApiOperation(value = "首页查询采购公告详细信息")
    @GetMapping("/getTenderNoticeByProject")
    public AjaxResult getTenderNoticeByProject(Long projectId){
        BusiTenderNotice vo = busiTenderNoticeService.selectByProject(projectId);
        if(vo!=null){
            return AjaxResult.success(vo);
        }
        return AjaxResult.error();
    }

    @ApiOperation(value = "供应商统计招投标信息")
    @GetMapping("/downloadTenderFile")
    public void downloadTenderFile(HttpServletRequest request, HttpServletResponse response, Long projectId){
        try {
            BusiTenderNotice q = new BusiTenderNotice();
            q.setProjectId(projectId);
            q.setDelFlag(0);
            BusiTenderNotice tenderNotice = busiTenderNoticeService.getOneIgnoreDeleted(q);
            saveUserDownload(request, tenderNotice);
            byte[] bs = busiTenderNoticeService.getTenderNoticeFileZip(tenderNotice.getNoticeId());
            genCode(response, bs);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveUserDownload(HttpServletRequest request, BusiTenderNotice tenderNotice){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        QueryWrapper<BusiTenderDocumentsDownload> busiTenderProjectQueryWrapper = new QueryWrapper<>();
        busiTenderProjectQueryWrapper.eq("project_id", tenderNotice.getProjectId());
        busiTenderProjectQueryWrapper.eq("bidder_id", loginUser.getEntId());
        busiTenderDocumentsDownloadService.remove(busiTenderProjectQueryWrapper);
        BusiTenderDocumentsDownload busiTenderDocumentsDownload=new BusiTenderDocumentsDownload();
        busiTenderDocumentsDownload.setProjectId(tenderNotice.getProjectId());
        busiTenderDocumentsDownload.setNoticeVersion(tenderNotice.getChangeNum());
        busiTenderDocumentsDownload.setBidderId(loginUser.getEntId());
        busiTenderDocumentsDownload.setBidderCode(loginUser.getUser().getEnt().getEntCode());
        busiTenderDocumentsDownload.setBidderName(loginUser.getUser().getEnt().getEntName());
        busiTenderDocumentsDownload.setDownloadIp(request.getRemoteAddr());
        busiTenderDocumentsDownload.setDownloadTime(new Date());
        busiTenderDocumentsDownloadService.save(busiTenderDocumentsDownload);
    }

    /**
     * 生成zip文件
     */
    private void genCode(HttpServletResponse response, byte[] data) throws IOException
    {
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"tenderFile.zip\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }


    /**
     * 查询开标中项目(开标室首页)
     * @param busiTenderNotice
     * @return
     */
    @ApiOperation(value = "查询开标中项目")
    @GetMapping("/duringBidOpeninglist")
    public AjaxResult duringBidOpeninglist(BusiTenderNotice busiTenderNotice){

        return busiTenderNoticeService.duringBidOpeninglist(busiTenderNotice,getLoginUser());
    }
    @ApiOperation(value = "查询开标中项目")
    @GetMapping("/duringBidOpeningInfo")
    public AjaxResult duringBidOpeningInfo( BusiTenderNotice busiTenderNotice){
        return busiTenderNoticeService.duringBidOpeningInfo(busiTenderNotice);
    }

    @ApiOperation(value = "查询开标中项目")
    @GetMapping("/getNoticeTypes")
    public AjaxResult getNoticeTypes(Long projectId){
        return busiTenderNoticeService.getNoticeTypes(projectId);
    }

    @ApiOperation(value = "查询开标中项目")
    @GetMapping("/push2Zcxh")
    public AjaxResult push2Zcxh(Long projectId){
        return busiTenderNoticeService.push2Zcxh(projectId);
    }
}
