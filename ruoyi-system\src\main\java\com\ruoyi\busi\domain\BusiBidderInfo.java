package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.eval.domain.EvalExpertEvaluationDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 参与投标人信息对象 busi_bidder_info
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ApiModel("参与投标人信息对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiBidderInfoMapper.BusiBidderInfoResult")
public class BusiBidderInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 参与投标人信息id
     */
    @ApiModelProperty("参与投标人信息id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long bidderInfoId;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @Excel(name = "项目id")
    private Long projectId;
    /**
     * 投标人id
     */
    @ApiModelProperty("投标人id")
    @Excel(name = "投标人id")
    private Long bidderId;
    /**
     * 投标人名称
     */
    @ApiModelProperty("投标人名称")
    @Excel(name = "投标人名称")
    private String bidderName;
    /**
     * 投标人代码
     */
    @ApiModelProperty("投标人代码")
    @Excel(name = "投标人代码")
    private String bidderCode;
    /**
     * 解密标记 0未解密 1解密成功 -1解密失败
     */
    @ApiModelProperty("解密标记 0未解密 1解密成功 -1解密失败")
    @Excel(name = "解密标记 0未解密 1解密成功 -1解密失败")
    private Integer decodeFlag;
    /**
     * 解密时间
     */
    @ApiModelProperty("解密时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "解密时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date decodeTime;
    /**
     * 投标金额
     */
    @ApiModelProperty("投标金额")
    @Excel(name = "投标金额")
    private BigDecimal bidderAmount;
    /**
     * 排名
     */
    @ApiModelProperty("排名")
    @Excel(name = "排名")
    private Integer ranking;
    /**
     * 得分
     */
    @ApiModelProperty("得分")
    @Excel(name = "得分")
    private BigDecimal score;
    /**
     * 是否中标 0未中标 1中标
     */
    @ApiModelProperty("是否中标 0未中标 1中标")
    @Excel(name = "是否中标 0未中标 1中标")
    private Integer isWin;
    /**
     * 删除标记 0正常 1删除
     */
    @ApiModelProperty("删除标记 0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;


    /**
     * 是否废标 0否1是
     */
    @ApiModelProperty("是否废标 0否1是")
    @Excel(name = "是否废标 0否1是")
    private Integer isAbandonedBid;
    /**
     * 废标原因
     */
    @ApiModelProperty("废标原因")
    @Excel(name = "废标原因")
    private String abandonedBidReason;

    @TableField(exist = false)
    private BusiTenderProject project;

    @TableField(exist = false)
    private boolean isWinCheckBox;
    public boolean getIsWinCheckBox(){
        return isWin != null && isWin == 1;
    }
    private String bidContactPerson;

    // 投标联系人
    private String headPerson;
    private String legalPerson;
    // 投标联系人联系
    private String bidContactPersonTel;
    // 投标联系人
    //@TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer signInStatus; // 注意：在Java中，tinyint通常映射为Byte类型
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signInTime;
    //g供应商输入解密密码
    @TableField(exist = false)
    private String supplierKey;
    @TableField(exist = false)
    private Long biddingId;
    @TableField(exist = false)
    private Integer xwqy;
    @TableField(exist = false)
    private List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails;
}
