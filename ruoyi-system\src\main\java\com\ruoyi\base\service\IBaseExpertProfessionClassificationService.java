package com.ruoyi.base.service;

import java.util.List;
import com.ruoyi.base.domain.BaseExpertProfessionClassification;
import com.ruoyi.base.domain.vo.ImportBaseExpertProfessionClassificationVo;
import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 专家专业分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface IBaseExpertProfessionClassificationService 
{
    /**
     * 查询专家专业分类
     * 
     * @param classificationCode 专家专业分类主键
     * @return 专家专业分类
     */
    public BaseExpertProfessionClassification selectBaseExpertProfessionClassificationByClassificationCode(String classificationCode);

    /**
     * 查询专家专业分类列表
     * 
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 专家专业分类集合
     */
    public List<BaseExpertProfessionClassification> selectBaseExpertProfessionClassificationList(BaseExpertProfessionClassification baseExpertProfessionClassification);

    /**
     * 新增专家专业分类
     * 
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 结果
     */
    public int insertBaseExpertProfessionClassification(BaseExpertProfessionClassification baseExpertProfessionClassification);

    /**
     * 修改专家专业分类
     * 
     * @param baseExpertProfessionClassification 专家专业分类
     * @return 结果
     */
    public int updateBaseExpertProfessionClassification(BaseExpertProfessionClassification baseExpertProfessionClassification);

    /**
     * 批量删除专家专业分类
     * 
     * @param classificationCodes 需要删除的专家专业分类主键集合
     * @return 结果
     */
    public int deleteBaseExpertProfessionClassificationByClassificationCodes(String[] classificationCodes);

    /**
     * 删除专家专业分类信息
     * 
     * @param classificationCode 专家专业分类主键
     * @return 结果
     */
    public int deleteBaseExpertProfessionClassificationByClassificationCode(String classificationCode);

    /**
     * 导入用户数据
     *
     * @param classificationList 专家专业分类数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importClassification(List<BaseExpertProfessionClassification> classificationList, Boolean isUpdateSupport, String operName);

    public String importCustomerClassification(List<ImportBaseExpertProfessionClassificationVo> list, boolean updateSupport, String operName);
}
