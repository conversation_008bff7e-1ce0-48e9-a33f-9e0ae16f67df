package com.ruoyi.busi.mapper;

import com.ruoyi.busi.domain.BusiTenderNotice;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.busi.domain.BusiVenueOccupy;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场地占用Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Mapper
public interface BusiVenueOccupyMapper extends BaseMapper<BusiVenueOccupy> {

    List<BusiVenueOccupy> getListIgnoreDeleted(@Param("info") BusiVenueOccupy busiVenueOccupy);
}