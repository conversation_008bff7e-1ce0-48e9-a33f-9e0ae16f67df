package com.ruoyi.base.service;

import java.util.List;
import com.ruoyi.base.domain.BaseEntQualification;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 企业资质Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface IBaseEntQualificationService extends IService<BaseEntQualification> {
    /**
     * 查询企业资质列表
     *
     * @param baseEntQualification 企业资质
     * @return 企业资质集合
     */
    public List<BaseEntQualification> selectList(BaseEntQualification baseEntQualification);
}