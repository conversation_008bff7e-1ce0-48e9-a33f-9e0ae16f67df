package com.ruoyi.busi.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 附件Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "附件管理")
@RestController
@RequestMapping("/attachment/info")
public class BusiAttachmentController extends BaseController {
    @Autowired
    private IBusiAttachmentService busiAttachmentService;

/**
 * 查询附件列表
 */
@PreAuthorize("@ss.hasPermi('attachment:info:list')")
@ApiOperation(value = "查询附件列表")
@GetMapping("/list")
    public TableDataInfo list(BusiAttachment busiAttachment) {
        startPage();
        List<BusiAttachment> list = busiAttachmentService.selectList(busiAttachment);
        return getDataTable(list);
    }

    /**
     * 导出附件列表
     */
    @PreAuthorize("@ss.hasPermi('attachment:info:export')")
    @Log(title = "附件", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出附件列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiAttachment busiAttachment) {
        List<BusiAttachment> list = busiAttachmentService.selectList(busiAttachment);
        ExcelUtil<BusiAttachment> util = new ExcelUtil<BusiAttachment>(BusiAttachment. class);
        util.exportExcel(response, list, "附件数据");
    }

    /**
     * 获取附件详细信息
     */
    @PreAuthorize("@ss.hasPermi('attachment:info:query')")
    @ApiOperation(value = "获取附件详细信息")
    @ApiImplicitParam(name = "attachmentId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{attachmentId}")
    public AjaxResult getInfo(@PathVariable("attachmentId")Long attachmentId) {
        return success(busiAttachmentService.getById(attachmentId));
    }

    /**
     * 新增附件
     */
    @Log(title = "附件", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增附件")
    @PostMapping
    public AjaxResult add(@RequestBody BusiAttachment busiAttachment) {
        busiAttachment.setCreateBy(getUsername());
        busiAttachment.setUpdateBy(getUsername());
        return toAjax(busiAttachmentService.save(busiAttachment));
    }

    /**
     * 修改附件
     */
    @Log(title = "附件", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改附件")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiAttachment busiAttachment) {
        busiAttachment.setUpdateBy(getUsername());
        return toAjax(busiAttachmentService.updateById(busiAttachment));
    }

    /**
     * 删除附件
     */
    @Log(title = "附件", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除附件")
    @DeleteMapping("/{attachmentIds}")
    public AjaxResult remove(@PathVariable Long[] attachmentIds) {
        return toAjax(busiAttachmentService.removeByIds(Arrays.asList(attachmentIds)));
    }
}
