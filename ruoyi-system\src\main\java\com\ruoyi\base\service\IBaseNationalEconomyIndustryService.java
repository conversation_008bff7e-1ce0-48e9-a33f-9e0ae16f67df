package com.ruoyi.base.service;

import java.util.List;
import com.ruoyi.base.domain.BaseNationalEconomyIndustry;

/**
 * 国民经济行业分类Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface IBaseNationalEconomyIndustryService 
{
    /**
     * 查询国民经济行业分类
     * 
     * @param industrCode 国民经济行业分类主键
     * @return 国民经济行业分类
     */
    public BaseNationalEconomyIndustry selectBaseNationalEconomyIndustryByIndustrCode(String industrCode);

    /**
     * 查询国民经济行业分类列表
     * 
     * @param baseNationalEconomyIndustry 国民经济行业分类
     * @return 国民经济行业分类集合
     */
    public List<BaseNationalEconomyIndustry> selectBaseNationalEconomyIndustryList(BaseNationalEconomyIndustry baseNationalEconomyIndustry);

    /**
     * 新增国民经济行业分类
     * 
     * @param baseNationalEconomyIndustry 国民经济行业分类
     * @return 结果
     */
    public int insertBaseNationalEconomyIndustry(BaseNationalEconomyIndustry baseNationalEconomyIndustry);

    /**
     * 修改国民经济行业分类
     * 
     * @param baseNationalEconomyIndustry 国民经济行业分类
     * @return 结果
     */
    public int updateBaseNationalEconomyIndustry(BaseNationalEconomyIndustry baseNationalEconomyIndustry);

    /**
     * 批量删除国民经济行业分类
     * 
     * @param industrCodes 需要删除的国民经济行业分类主键集合
     * @return 结果
     */
    public int deleteBaseNationalEconomyIndustryByIndustrCodes(String[] industrCodes);

    /**
     * 删除国民经济行业分类信息
     * 
     * @param industrCode 国民经济行业分类主键
     * @return 结果
     */
    public int deleteBaseNationalEconomyIndustryByIndustrCode(String industrCode);
}
