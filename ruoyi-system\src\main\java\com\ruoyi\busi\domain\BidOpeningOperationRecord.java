package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 开标操作记录对象 bid_opening_operation_record
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@ApiModel("开标操作记录对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BidOpeningOperationRecordMapper.BidOpeningOperationRecordResult")
public class BidOpeningOperationRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作ID
     */
    @ApiModelProperty("操作ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long operationId;
    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    @Excel(name = "项目ID")
    private Long projectId;
    /**
     * 操作人ID
     */
    @ApiModelProperty("操作人ID")
    @Excel(name = "操作人ID")
    private Long userId;
    /**
     * 操作类型，数字字典{开标准备、投标人公式、标书解密、唱标、开标结束}
     */
    @ApiModelProperty("操作类型，数字字典{开标准备、投标人公式、标书解密、唱标、开标结束}")
    @Excel(name = "操作类型，数字字典{开标准备、投标人公式、标书解密、唱标、开标结束}")
    private Long operationType;
    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;
    /**
     * 解密时间
     */
    @ApiModelProperty("解密时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "解密时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date decryptionTime;
    /**
     * 删除标记 0正常 1删除
     */
    @ApiModelProperty("删除标记 0正常 1删除")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    //供应商跳转页面状态
    @TableField(exist =  false)
    private Integer supplierPageStatus;
    //供应商跳转页面状态
    @TableField(exist =  false)
    private BusiTenderProject project;
    //代理机构
    @TableField(exist =  false)
    private Integer agencyPageStatus;

    @TableField(exist =  false)
    private Integer minutes;
    @TableField(exist =  false)
    private String systemTime;

}
