package com.ruoyi.busi.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.service.IBusiAttachmentService;
import com.ruoyi.busi.service.IBusiBidderInfoService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiBidEvaluation;
import com.ruoyi.busi.service.IBusiBidEvaluationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 评标记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Api(tags = "评标记录管理")
@RestController
@RequestMapping("/bid/evaluation")
public class BusiBidEvaluationController extends BaseController {
    @Autowired
    private IBusiBidEvaluationService busiBidEvaluationService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    private static final Logger log = LoggerFactory.getLogger(BusiBidEvaluationController.class);

    /**
 * 查询评标记录列表
 */
@PreAuthorize("@ss.hasPermi('bid:evaluation:list')")
@ApiOperation(value = "查询评标记录列表")
@GetMapping("/list")
    public TableDataInfo list(BusiBidEvaluation busiBidEvaluation) {
        startPage();
    busiBidEvaluation.setDelFlag(0);
        List<BusiBidEvaluation> list = busiBidEvaluationService.selectList(busiBidEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出评标记录列表
     */
    @PreAuthorize("@ss.hasPermi('bid:evaluation:export')")
    @Log(title = "评标记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出评标记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiBidEvaluation busiBidEvaluation) {
        List<BusiBidEvaluation> list = busiBidEvaluationService.selectList(busiBidEvaluation);
        ExcelUtil<BusiBidEvaluation> util = new ExcelUtil<BusiBidEvaluation>(BusiBidEvaluation. class);
        util.exportExcel(response, list, "评标记录数据");
    }

    /**
     * 获取评标记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('bid:evaluation:query')")
    @ApiOperation(value = "获取评标记录详细信息")
    @ApiImplicitParam(name = "bidEvaluationId", value = "评标记录id", required = true, dataType = "Long")
    @GetMapping(value = "/{bidEvaluationId}")
    public AjaxResult getInfo(@PathVariable("bidEvaluationId")Long bidEvaluationId) {
        BusiBidEvaluation byId = busiBidEvaluationService.getById(bidEvaluationId);
        List<BusiBidderInfo> projectId = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", byId.getProjectId()));
        byId.setProjectInfoList(projectId);
        byId.setAttachments(iBusiAttachmentService.getByBusiId(byId.getBidEvaluationId()));
        return success(byId);
    }

    /**
     * 新增评标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:evaluation:add')")
    @Log(title = "评标记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增评标记录")
    @PostMapping
    public AjaxResult add(@RequestBody BusiBidEvaluation busiBidEvaluation) {
        List<BusiBidEvaluation> projectId = busiBidEvaluationService.list(new QueryWrapper<BusiBidEvaluation>().eq("project_id", busiBidEvaluation.getProjectId()));
        if (projectId!=null){
            return AjaxResult.error("该项目已存在评审记录，如需修改，请先删除再新增！");
        }
        busiBidEvaluation.setCreateBy(getUsername());
        busiBidEvaluation.setUpdateBy(getUsername());
        return busiBidEvaluationService.saveBatchBusiBidderInfo(busiBidEvaluation);
    }


    @Log(title = "评标记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增评标记录")
    @PostMapping(value ="/saveBatchBusiBidderInfo" )
    public AjaxResult saveBatchBusiBidderInfo(@RequestBody BusiBidEvaluation busiBidEvaluation) {
        busiBidEvaluation.setCreateBy(getUsername());
        busiBidEvaluation.setUpdateBy(getUsername());
        return  busiBidEvaluationService.saveBatchBusiBidderInfo(busiBidEvaluation);
    }


    /**
     * 修改评标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:evaluation:edit')")
    @Log(title = "评标记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改评标记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiBidEvaluation busiBidEvaluation) {
        busiBidEvaluation.setUpdateBy(getUsername());
        return toAjax(busiBidEvaluationService.updateById(busiBidEvaluation));
    }

    /**
     * 删除评标记录
     */
    @PreAuthorize("@ss.hasPermi('bid:evaluation:remove')")
    @Log(title = "评标记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除评标记录")
    @DeleteMapping("/{bidEvaluationIds}")
    public AjaxResult remove(@PathVariable Long[] bidEvaluationIds) {
        return AjaxResult.success(busiBidEvaluationService.removeBusiBidderInfo(bidEvaluationIds));
    }
    /**
     * 评审记录文件下载记录
     * @param params
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/downLoadFile")
    public AjaxResult downLoadFile(@RequestParam  Map<String,Object> params, HttpServletRequest request, HttpServletResponse response) {
        System.out.println(request.getRemoteAddr());
        System.out.println(AddressUtils.getRealAddressByIP(request.getRemoteAddr()));
        System.out.println(request.getRemoteAddr());
       return AjaxResult.success();
    }

}
