package com.ruoyi.busi.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.busi.domain.BusiTenderDocumentsDownload;
import com.ruoyi.busi.service.IBusiTenderDocumentsDownloadService;
import com.ruoyi.common.utils.poi.ExcelUtil;
    import com.ruoyi.common.core.page.TableDataInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 采购文件下载记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Api(tags = "采购文件下载记录管理")
@RestController
@RequestMapping("/documents/download")
public class BusiTenderDocumentsDownloadController extends BaseController {
    @Autowired
    private IBusiTenderDocumentsDownloadService busiTenderDocumentsDownloadService;
    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;

/**
 * 查询采购文件下载记录列表
 */
@PreAuthorize("@ss.hasPermi('documents:download:list')")
@ApiOperation(value = "查询采购文件下载记录列表")
@GetMapping("/list")
    public TableDataInfo list(BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        startPage();
        List<BusiTenderDocumentsDownload> list = busiTenderDocumentsDownloadService.selectList(busiTenderDocumentsDownload);
        return getDataTable(list);
    }

    /**
     * 查询采购文件下载记录列表
     */
    @ApiOperation(value = "供应商我的订单")
    @GetMapping("/myOrder")
    public TableDataInfo myOrder(BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        startPage();
        List<BusiTenderDocumentsDownload> list = busiTenderDocumentsDownloadService.selectList(busiTenderDocumentsDownload);
        if (!list.isEmpty()){
            List<Long> projectIds = list.stream().map(item -> item.getProjectId()).collect(Collectors.toList());
            BusiTenderProject projectQuery = new BusiTenderProject();
            projectQuery.setParams(busiTenderDocumentsDownload.getParams());
            if(projectIds.size()==0){
                List list1 =new ArrayList<>();
                list1.add(0);
                projectQuery.getParams().put("projectIds",list1);
            }else{
                projectQuery.getParams().put("projectIds",projectIds);
            }
            projectQuery.getParams().put("isScope",1);
            List<BusiTenderProject> busiTenderProjects = iBusiTenderProjectService.selectList(projectQuery);
            for (BusiTenderProject project : busiTenderProjects) {
                BusiTenderNotice notice = busiTenderNoticeService.selectByProject(project.getProjectId());
                if (notice != null) {
                    project.setBidOpeningTime(notice.getBidOpeningTime());
                }
            }
         return    getDataTable(busiTenderProjects);
        }

        return null;
    }

    /**
     * 导出采购文件下载记录列表
     */
    @PreAuthorize("@ss.hasPermi('documents:download:export')")
    @Log(title = "采购文件下载记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购文件下载记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        List<BusiTenderDocumentsDownload> list = busiTenderDocumentsDownloadService.selectList(busiTenderDocumentsDownload);
        ExcelUtil<BusiTenderDocumentsDownload> util = new ExcelUtil<BusiTenderDocumentsDownload>(BusiTenderDocumentsDownload. class);
        util.exportExcel(response, list, "采购文件下载记录数据");
    }

    /**
     * 获取采购文件下载记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('documents:download:query')")
    @ApiOperation(value = "获取采购文件下载记录详细信息")
    @ApiImplicitParam(name = "downloadId", value = "下载采购文件id", required = true, dataType = "Long")
    @GetMapping(value = "/{downloadId}")
    public AjaxResult getInfo(@PathVariable("downloadId")Long downloadId) {
        return success(busiTenderDocumentsDownloadService.getById(downloadId));
    }

    /**
     * 新增采购文件下载记录
     */
    @PreAuthorize("@ss.hasPermi('documents:download:add')")
    @Log(title = "采购文件下载记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购文件下载记录")
    @PostMapping
    public AjaxResult add(@RequestBody BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        busiTenderDocumentsDownload.setCreateBy(getUsername());
        busiTenderDocumentsDownload.setUpdateBy(getUsername());
        return toAjax(busiTenderDocumentsDownloadService.save(busiTenderDocumentsDownload));
    }

    /**
     * 修改采购文件下载记录
     */
    @PreAuthorize("@ss.hasPermi('documents:download:edit')")
    @Log(title = "采购文件下载记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购文件下载记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiTenderDocumentsDownload busiTenderDocumentsDownload) {
        busiTenderDocumentsDownload.setUpdateBy(getUsername());
        return toAjax(busiTenderDocumentsDownloadService.updateById(busiTenderDocumentsDownload));
    }

    /**
     * 删除采购文件下载记录
     */
    @PreAuthorize("@ss.hasPermi('documents:download:remove')")
    @Log(title = "采购文件下载记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购文件下载记录")
    @DeleteMapping("/{downloadIds}")
    public AjaxResult remove(@PathVariable Long[] downloadIds) {
        return toAjax(busiTenderDocumentsDownloadService.removeByIds(Arrays.asList(downloadIds)));
    }
}
