package com.ruoyi.web.controller.home.vo;

import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DataStatisticsVo {
    /*
    供应商
     */
    private BigDecimal totalAmount=BigDecimal.ZERO; // 中标总金额
    private Integer registerNum=0; //报名中数量
    private Integer totalNum=0; // 中标总数量 -- 累计订单数量
    private Integer bidNum=0; //投标数量

    /*
    采购人
     */
    private BigDecimal economizeAmount=BigDecimal.ZERO; // 中标节约金额
    private Integer intentionNum=0; // 采购意向数量
    private Integer projectNum=0; // 采购意向数量
}
