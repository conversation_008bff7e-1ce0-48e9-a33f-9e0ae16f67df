package com.ruoyi.busi.domain.vo;

import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class PortalProcessVo {

    /**
     * 数据类型
     */
    private String dataType;
    /**
     * 数据类型名称
     */
    private String dataTypeName;
    /**
     * 发布时间
     */
    private String releaseTime;
    /**
     * 当前状态
     */
    private Boolean done;

    private PortalProcessVo(String dataType, String releaseTime, Boolean done){
        this.dataType = dataType;
        this.releaseTime = releaseTime;
        this.done = done;
        this.dataTypeName = getTypeName(dataType);
    }

    public static PortalProcessVo exist(String dataType, Date releaseTime){
        String time = new SimpleDateFormat("yyyy-MM-dd").format(releaseTime);
        return new PortalProcessVo(dataType, time, true);
    }

    public static PortalProcessVo notExist(String dataType){
        return new PortalProcessVo(dataType,  "", false);
    }

    private String getTypeName(String dataType) {
        switch (dataType) {
            case "20":
                return "采购公告";
            case "21":
                return "变更公告";
            case "60":
                return "结果公告";
            case "-2":
                return "取消公告";
            default:
                return "";
        }
    }
}
