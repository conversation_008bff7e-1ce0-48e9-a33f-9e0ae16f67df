package com.ruoyi.busi.controller;

import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import freemarker.template.TemplateException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 采购项目信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "采购项目信息管理")
@RestController
@RequestMapping("/tender/project")
public class BusiTenderProjectController extends BaseController {
    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;



    /**
     * 初始化新项目的用户评分办法因素
     */
    @PreAuthorize("@ss.hasPermi('method:uitem:init')")
    @Log(title = "初始化新项目的用户评分办法因素", businessType = BusinessType.INSERT)
    @ApiOperation(value = "初始化新项目的用户评分办法因素")
    @PostMapping("/initNewProjectUitem/{projectId}")
    public AjaxResult initNewProjectUitem(@PathVariable("projectId") Long  projectId) {
        scoringMethodUitemService.initNewProjectUitem(projectId);
        return success();
    }

    /**
     * 查询采购项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('tender:project:list')")
    @ApiOperation(value = "查询采购项目信息列表")
    @GetMapping("/list")
    public TableDataInfo list(BusiTenderProject busiTenderProject) {
        startPage();
        List<BusiTenderProject> list = busiTenderProjectService.selectList(busiTenderProject);
        return getDataTable(list);
    }

    /**
     * 查询未抽取专家的采购项目信息列表
     */
    @ApiOperation(value = "查询未抽取专家的采购项目信息列表")
    @GetMapping("/getListByExtractionType")
    public AjaxResult getListByExtractionType(BusiTenderProject busiTenderProject) {
        List<BusiTenderProject> list = busiTenderProjectService.getListByExtractionType(busiTenderProject);
        return AjaxResult.success(list);
    }



    /**
     * 供应商查看已下载投标文件的项目
     */
    @GetMapping("/supplierLookProdect")
    public AjaxResult supplierLookProdect(){
        return busiTenderProjectService.supplierLookProdect(getLoginUser());
    }
    /**
     * 供应商首页查看已报名项目
     */
    @GetMapping("/supplierViewProdect")
    public AjaxResult supplierViewProdect(){
        return busiTenderProjectService.supplierViewProdect(getLoginUser());
    }
    //查询已中标的项目
    @GetMapping("/supplierisWinProdect")
    public AjaxResult supplierisWinProdect(){
        return busiTenderProjectService.supplierisWinProdect(getLoginUser());
    }
    //二维码扫描信息
    @GetMapping("/codePage/{projectId}")
    public AjaxResult codePage(@PathVariable("projectId")Long projectId){
        return busiTenderProjectService.codePage(projectId);
    }



    /**
     * 导出采购项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('tender:project:export')")
    @Log(title = "采购项目信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出采购项目信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusiTenderProject busiTenderProject) {
        List<BusiTenderProject> list = busiTenderProjectService.selectList(busiTenderProject);
        ExcelUtil<BusiTenderProject> util = new ExcelUtil<BusiTenderProject>(BusiTenderProject. class);
        util.exportExcel(response, list, "采购项目信息数据");
    }

    /**
     * 获取采购项目信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('tender:project:query')")
    @ApiOperation(value = "获取采购项目信息详细信息")
    @ApiImplicitParam(name = "projectId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId")Long projectId) {
        BusiTenderProject project = busiTenderProjectService.getByIdHasAttachment(projectId);

        return success(project);
    }

    /**
     * 获取采购项目信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('tender:project:query')")
    @ApiOperation(value = "获取采购项目信息详细信息")
    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo2(Long projectId) {
        BusiTenderVo project = busiTenderProjectService.getAllById(projectId);
        return success(project);
    }

    /**
     * 新增采购项目信息
     */
    @PreAuthorize("@ss.hasPermi('tender:project:add')")
    @Log(title = "采购项目信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增采购项目信息")
    @PostMapping
    public AjaxResult add(@RequestBody BusiTenderProject busiTenderProject) {
        busiTenderProject.setCreateBy(getUsername());
        busiTenderProject.setUpdateBy(getUsername());
        return busiTenderProjectService.saveHaveAttachment(busiTenderProject);
    }

    /**
     * 修改采购项目信息
     */
    @PreAuthorize("@ss.hasPermi('tender:project:edit')")
    @Log(title = "采购项目信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改采购项目信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BusiTenderProject busiTenderProject) {
        busiTenderProject.setUpdateBy(getUsername());
        return busiTenderProjectService.updateByIdHaveAttachment(busiTenderProject);
    }

    /**
     * 删除采购项目信息
     */
    @PreAuthorize("@ss.hasPermi('tender:project:remove')")
    @Log(title = "采购项目信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除采购项目信息")
    @DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable Long[] projectIds) {
        return toAjax(busiTenderProjectService.removeByIds(Arrays.asList(projectIds)));
    }


    @GetMapping(value = "/freeMarker/{projectId}")
    public String freeMarker(@PathVariable("projectId") Long  projectId) throws IOException, TemplateException {
        /*map.put("xmmc", "Joe");
        map.put("cgr", 1);

        List<Map<String, Object>> friends = new ArrayList<Map<String,Object>>();
        Map<String, Object> friend = new HashMap<String, Object>();
        friend.put("name", "Jack");
        friend.put("age", 22);
        friends.add(friend);
        friend = new HashMap<String, Object>();
        friend.put("name", "Tom");
        friend.put("age", 21);
        friends.add(friend);
        map.put("friends", friends);*/
        busiTenderProjectService.freeMarker(projectId);
        return "openRecord";
    }

}
