package com.ruoyi.base.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.base.domain.vo.ImportBaseExpertProfessionClassificationVo;
import com.ruoyi.common.core.domain.entity.SysUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.base.domain.BaseExpertProfessionClassification;
import com.ruoyi.base.service.IBaseExpertProfessionClassificationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 专家专业分类Controller
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/profession/classification")
public class BaseExpertProfessionClassificationController extends BaseController
{
    @Autowired
    private IBaseExpertProfessionClassificationService baseExpertProfessionClassificationService;

    /**
     * 查询专家专业分类列表
     */
    @PreAuthorize("@ss.hasPermi('profession:classification:list')")
    @GetMapping("/list")
    public TableDataInfo list(BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        startPage();
        List<BaseExpertProfessionClassification> list = baseExpertProfessionClassificationService.selectBaseExpertProfessionClassificationList(baseExpertProfessionClassification);
        return getDataTable(list);
    }

    /**
     * 导出专家专业分类列表
     */
    @PreAuthorize("@ss.hasPermi('profession:classification:export')")
    @Log(title = "专家专业分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        List<BaseExpertProfessionClassification> list = baseExpertProfessionClassificationService.selectBaseExpertProfessionClassificationList(baseExpertProfessionClassification);
        ExcelUtil<BaseExpertProfessionClassification> util = new ExcelUtil<BaseExpertProfessionClassification>(BaseExpertProfessionClassification.class);
        util.exportExcel(response, list, "专家专业分类数据");
    }


    @Log(title = "专家专业分类", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<BaseExpertProfessionClassification> util
                = new ExcelUtil<BaseExpertProfessionClassification>(BaseExpertProfessionClassification.class);
        List<BaseExpertProfessionClassification> list = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = baseExpertProfessionClassificationService.importClassification(list, updateSupport, operName);
        return success(message);
    }

    @Log(title = "专家专业分类", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importCustmoerData")
    public AjaxResult importCustmoerData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ImportBaseExpertProfessionClassificationVo> util
                = new ExcelUtil<ImportBaseExpertProfessionClassificationVo>(ImportBaseExpertProfessionClassificationVo.class);
        List<ImportBaseExpertProfessionClassificationVo> list = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = baseExpertProfessionClassificationService.importCustomerClassification(list, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/customerImportTemplate")
    public void customerImportTemplate(HttpServletResponse response)
    {
        ExcelUtil<ImportBaseExpertProfessionClassificationVo> util = new ExcelUtil<ImportBaseExpertProfessionClassificationVo>(ImportBaseExpertProfessionClassificationVo.class);
        util.importTemplateExcel(response, "专家专业分类数据");
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<BaseExpertProfessionClassification> util = new ExcelUtil<BaseExpertProfessionClassification>(BaseExpertProfessionClassification.class);
        util.importTemplateExcel(response, "专家专业分类数据");
    }

    /**
     * 获取专家专业分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('profession:classification:query')")
    @GetMapping(value = "/{classificationCode}")
    public AjaxResult getInfo(@PathVariable("classificationCode") String classificationCode)
    {
        return success(baseExpertProfessionClassificationService.selectBaseExpertProfessionClassificationByClassificationCode(classificationCode));
    }

    /**
     * 新增专家专业分类
     */
    @PreAuthorize("@ss.hasPermi('profession:classification:add')")
    @Log(title = "专家专业分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        baseExpertProfessionClassification.setCreateBy(getUsername());
        return toAjax(baseExpertProfessionClassificationService.insertBaseExpertProfessionClassification(baseExpertProfessionClassification));
    }

    /**
     * 修改专家专业分类
     */
    @PreAuthorize("@ss.hasPermi('profession:classification:edit')")
    @Log(title = "专家专业分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BaseExpertProfessionClassification baseExpertProfessionClassification)
    {
        baseExpertProfessionClassification.setUpdateBy(getUsername());
        return toAjax(baseExpertProfessionClassificationService.updateBaseExpertProfessionClassification(baseExpertProfessionClassification));
    }

    /**
     * 删除专家专业分类
     */
    @PreAuthorize("@ss.hasPermi('profession:classification:remove')")
    @Log(title = "专家专业分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{classificationCodes}")
    public AjaxResult remove(@PathVariable String[] classificationCodes)
    {
        return toAjax(baseExpertProfessionClassificationService.deleteBaseExpertProfessionClassificationByClassificationCodes(classificationCodes));
    }
}
